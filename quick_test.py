#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速测试NetCDF文件
"""

import netCDF4
import numpy as np
import os

def quick_test():
    nc_file = "mig01.nc"
    
    if not os.path.exists(nc_file):
        print(f"文件不存在: {nc_file}")
        return
    
    print(f"测试文件: {nc_file}")
    print(f"文件大小: {os.path.getsize(nc_file) / (1024*1024):.1f} MB")
    
    try:
        with netCDF4.Dataset(nc_file, 'r') as ncfile:
            print(f"格式: {ncfile.data_model}")
            
            print("\n维度:")
            for name, dim in ncfile.dimensions.items():
                print(f"  {name}: {len(dim)}")
            
            print("\n变量:")
            for name, var in ncfile.variables.items():
                print(f"  {name}: {var.dimensions} {var.shape}")
            
            print("\n关键属性:")
            if hasattr(ncfile, 'Conventions'):
                print(f"  Conventions: {ncfile.Conventions}")
            if hasattr(ncfile, 'title'):
                print(f"  Title: {ncfile.title}")
            if hasattr(ncfile, 'data_coverage_percent'):
                print(f"  数据覆盖率: {ncfile.data_coverage_percent:.1f}%")
            
            # 测试数据读取
            if 'amplitude' in ncfile.variables:
                amp = ncfile.variables['amplitude']
                print(f"\n振幅数据:")
                print(f"  形状: {amp.shape}")
                print(f"  数据类型: {amp.dtype}")
                
                # 读取一小部分数据
                sample_data = amp[0, :10, :10]
                print(f"  样本数据范围: {np.nanmin(sample_data):.3f} - {np.nanmax(sample_data):.3f}")
                
        print("\n✓ 文件测试成功！")
        print("\n可以尝试在ArcGIS Pro中打开此文件:")
        print("1. 添加数据 -> NetCDF文件")
        print("2. 选择 mig01.nc")
        print("3. 选择 amplitude 变量")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    quick_test()
