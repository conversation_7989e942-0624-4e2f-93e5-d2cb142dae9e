#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证CGCS2000投影信息
检查NetCDF文件中的CGCS2000 3度带第37带投影设置
"""

import netCDF4
import numpy as np
import os

def verify_cgcs2000_projection():
    """验证CGCS2000投影信息"""
    nc_file = "mig01_cgcs2000.nc"
    
    if not os.path.exists(nc_file):
        print(f"文件不存在: {nc_file}")
        return
    
    print("验证CGCS2000投影信息")
    print("=" * 50)
    
    try:
        with netCDF4.Dataset(nc_file, 'r') as ncfile:
            print(f"文件: {nc_file}")
            print(f"大小: {os.path.getsize(nc_file) / (1024*1024):.1f} MB")
            
            # 检查坐标范围
            x = ncfile.variables['x'][:]
            y = ncfile.variables['y'][:]
            z = ncfile.variables['z'][:]
            
            print(f"\n坐标信息:")
            print(f"X坐标范围: {x.min():,.0f} - {x.max():,.0f}")
            print(f"Y坐标范围: {y.min():,.0f} - {y.max():,.0f}")
            print(f"Z(时间)范围: {z.min():.1f} - {z.max():.1f} ms")
            
            # 检查投影信息
            print(f"\n投影信息:")
            if 'crs' in ncfile.variables:
                crs = ncfile.variables['crs']
                print(f"✓ CRS变量存在")
                
                # 检查投影参数
                if hasattr(crs, 'grid_mapping_name'):
                    print(f"  网格映射: {crs.grid_mapping_name}")
                
                if hasattr(crs, 'longitude_of_central_meridian'):
                    print(f"  中央子午线: {crs.longitude_of_central_meridian}°")
                
                if hasattr(crs, 'false_easting'):
                    print(f"  东偏移: {crs.false_easting:,.0f} 米")
                
                if hasattr(crs, 'false_northing'):
                    print(f"  北偏移: {crs.false_northing:,.0f} 米")
                
                if hasattr(crs, 'scale_factor_at_central_meridian'):
                    print(f"  比例因子: {crs.scale_factor_at_central_meridian}")
                
                # 检查ESRI投影字符串
                if hasattr(crs, 'spatial_ref'):
                    spatial_ref = crs.spatial_ref
                    print(f"\n✓ ESRI投影字符串存在")
                    if 'CGCS2000_3_Degree_GK_Zone_37' in spatial_ref:
                        print(f"  ✓ 正确识别为CGCS2000 3度带第37带")
                    else:
                        print(f"  ⚠ 投影字符串可能不正确")
                    
                    # 显示关键参数
                    if 'Central_Meridian",111' in spatial_ref:
                        print(f"  ✓ 中央子午线: 111°")
                    if 'False_Easting",500000' in spatial_ref:
                        print(f"  ✓ 东偏移: 500,000 米")
                    if 'CGCS2000' in spatial_ref:
                        print(f"  ✓ 基准面: CGCS2000")
                    if 'Gauss_Kruger' in spatial_ref:
                        print(f"  ✓ 投影方法: 高斯-克吕格")
            else:
                print(f"✗ 缺少CRS变量")
            
            # 检查全局属性
            print(f"\n全局属性:")
            if hasattr(ncfile, 'references'):
                print(f"  参考系统: {ncfile.references}")
            
            if hasattr(ncfile, 'coordinate_system'):
                print(f"  坐标系统: {ncfile.coordinate_system}")
            
            if hasattr(ncfile, 'central_meridian'):
                print(f"  中央子午线: {ncfile.central_meridian}°")
            
            if hasattr(ncfile, 'projection_zone'):
                print(f"  投影带号: {ncfile.projection_zone}")
            
            if hasattr(ncfile, 'spatial_ref'):
                print(f"  ✓ 全局spatial_ref属性存在")
            
            # 验证坐标系统
            print(f"\n坐标系统验证:")
            
            # 检查X坐标是否在第37带范围内
            # 第37带的理论X坐标范围应该在37,000,000 + 实际坐标
            x_in_zone = 37000000 <= x.min() <= 38000000 and 37000000 <= x.max() <= 38000000
            print(f"  X坐标在第37带范围: {'✓ 是' if x_in_zone else '✗ 否'}")
            
            # 检查Y坐标是否合理（中国境内）
            y_reasonable = 3000000 <= y.min() <= 6000000 and 3000000 <= y.max() <= 6000000
            print(f"  Y坐标在合理范围: {'✓ 是' if y_reasonable else '✗ 否'}")
            
            # 检查中央子午线是否正确（第37带应该是111°）
            if hasattr(ncfile, 'central_meridian'):
                cm_correct = abs(ncfile.central_meridian - 111.0) < 0.1
                print(f"  中央子午线正确: {'✓ 是' if cm_correct else '✗ 否'}")
            
            print(f"\n数据统计:")
            if 'amplitude' in ncfile.variables:
                amp = ncfile.variables['amplitude']
                print(f"  振幅数据: {amp.shape}")
                print(f"  数据类型: {amp.dtype}")
                
                # 读取一小部分数据测试
                sample = amp[0, :10, :10]
                valid_count = np.sum(~np.isnan(sample))
                print(f"  样本有效数据: {valid_count}/{sample.size}")
            
            print(f"\n🎉 CGCS2000投影验证完成！")
            print(f"现在可以在ArcGIS Pro中使用，坐标系统将被正确识别为:")
            print(f"CGCS2000 3 Degree GK Zone 37")
            
    except Exception as e:
        print(f"验证失败: {e}")
        import traceback
        traceback.print_exc()

def show_projection_details():
    """显示投影详细信息"""
    print(f"\nCGCS2000 3度带第37带详细信息:")
    print("=" * 40)
    print(f"投影名称: CGCS2000_3_Degree_GK_Zone_37")
    print(f"投影方法: 高斯-克吕格 (Gauss-Kruger)")
    print(f"基准面: 中国大地坐标系统2000 (CGCS2000)")
    print(f"椭球体: CGCS2000椭球")
    print(f"中央子午线: 111°E (37 × 3 = 111)")
    print(f"投影带号: 37")
    print(f"东偏移: 500,000 米")
    print(f"北偏移: 0 米")
    print(f"比例因子: 1.0")
    print(f"原点纬度: 0°")
    
    print(f"\n坐标范围说明:")
    print(f"X坐标: 37,000,000 + 实际东坐标")
    print(f"Y坐标: 实际北坐标")
    print(f"适用区域: 东经109.5° - 112.5°")

if __name__ == "__main__":
    verify_cgcs2000_projection()
    show_projection_details()
