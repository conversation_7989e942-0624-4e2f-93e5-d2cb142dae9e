#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试NetCDF文件读取
验证03-sgync.py生成的NC文件是否正确
"""

import numpy as np
import os

# 尝试导入netCDF4
try:
    import netCDF4
    HAS_NETCDF4 = True
except ImportError:
    HAS_NETCDF4 = False
    print("警告: 未安装netCDF4库")

def test_nc_file(nc_file):
    """测试NetCDF文件"""
    print(f"测试NetCDF文件: {nc_file}")
    
    if not os.path.exists(nc_file):
        print(f"错误: 文件不存在: {nc_file}")
        return False
    
    if not HAS_NETCDF4:
        print("无法测试NetCDF文件，需要安装netCDF4库")
        return False
    
    try:
        with netCDF4.Dataset(nc_file, 'r') as ncfile:
            print(f"文件大小: {os.path.getsize(nc_file) / (1024*1024):.1f} MB")
            print(f"格式: {ncfile.data_model}")
            
            # 显示维度信息
            print("\n维度信息:")
            for dim_name, dim in ncfile.dimensions.items():
                print(f"  {dim_name}: {len(dim)}")
            
            # 显示变量信息
            print("\n变量信息:")
            for var_name, var in ncfile.variables.items():
                print(f"  {var_name}: {var.shape} ({var.dtype})")
                if hasattr(var, 'long_name'):
                    print(f"    描述: {var.long_name}")
                if hasattr(var, 'units'):
                    print(f"    单位: {var.units}")
            
            # 显示全局属性
            print("\n全局属性:")
            for attr_name in ncfile.ncattrs():
                attr_value = getattr(ncfile, attr_name)
                print(f"  {attr_name}: {attr_value}")
            
            # 测试数据读取
            print("\n数据测试:")
            if 'amplitude' in ncfile.variables:
                amp_var = ncfile.variables['amplitude']
                print(f"振幅数据形状: {amp_var.shape}")
                
                # 读取一小部分数据进行测试
                if len(amp_var.shape) == 3:  # 3D数据
                    test_data = amp_var[0, 0, :10]  # 读取第一个inline第一个crossline的前10个时间样点
                    print(f"测试数据 (前10个样点): {test_data}")
                    print(f"数据范围: {np.nanmin(amp_var[:])} 到 {np.nanmax(amp_var[:])}")
                elif len(amp_var.shape) == 2:  # 2D数据
                    test_data = amp_var[0, :10]  # 读取第一道的前10个时间样点
                    print(f"测试数据 (前10个样点): {test_data}")
                    print(f"数据范围: {np.nanmin(amp_var[:])} 到 {np.nanmax(amp_var[:])}")
            
            # 检查道头信息
            if 'trace_headers' in ncfile.groups:
                print("\n道头信息:")
                header_group = ncfile.groups['trace_headers']
                for var_name in header_group.variables:
                    var = header_group.variables[var_name]
                    print(f"  {var_name}: {var.shape}")
            
        print("\nNetCDF文件测试成功!")
        return True
        
    except Exception as e:
        print(f"NetCDF文件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_numpy_files(base_name):
    """测试numpy文件"""
    print(f"\n测试numpy文件 (基础名: {base_name})")
    
    files_to_test = [
        f"{base_name}_data.npy",
        f"{base_name}_inlines.npy", 
        f"{base_name}_crosslines.npy",
        f"{base_name}_x_coords.npy",
        f"{base_name}_y_coords.npy",
        f"{base_name}_grid.npy"
    ]
    
    for filename in files_to_test:
        if os.path.exists(filename):
            try:
                data = np.load(filename, allow_pickle=True)
                size_mb = os.path.getsize(filename) / (1024*1024)
                print(f"  {filename}: {data.shape if hasattr(data, 'shape') else type(data)} ({size_mb:.1f} MB)")
                
                if filename.endswith('_data.npy'):
                    print(f"    数据类型: {data.dtype}")
                    print(f"    数据范围: {np.nanmin(data)} 到 {np.nanmax(data)}")
                    print(f"    NaN数量: {np.sum(np.isnan(data))}")
                elif filename.endswith('_grid.npy'):
                    if isinstance(data, dict):
                        print(f"    网格信息: {data}")
                    else:
                        print(f"    网格数据: {data}")
                        
            except Exception as e:
                print(f"  {filename}: 读取失败 - {e}")
        else:
            print(f"  {filename}: 文件不存在")

def main():
    """主函数"""
    print("NetCDF和numpy文件测试工具")
    print("=" * 50)
    
    # 测试默认文件
    base_name = "mig01"
    nc_file = f"{base_name}.nc"
    
    # 测试NetCDF文件
    test_nc_file(nc_file)
    
    # 测试numpy文件
    test_numpy_files(base_name)
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
