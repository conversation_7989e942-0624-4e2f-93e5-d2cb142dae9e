# SGY到X-Y-Time格式转换工具

## 概述

`03-sgync-simple.py` 是一个将SEG-Y地震数据转换为X-Y-Time格式的工具。与传统的Inline-Crossline格式不同，这个工具保持原始数据量不变，直接使用X、Y坐标和时间轴来组织数据。

## 主要特点

- **保持原始数据量**: 不进行插值或重采样，完全保留原始SGY文件的数据点
- **X-Y-Time格式**: 使用真实的X、Y坐标而不是Inline/Crossline编号
- **多种输出格式**: 同时生成numpy文件和NetCDF文件
- **完整的元数据**: 保留所有坐标信息和时间轴数据

## 输出文件格式

### Numpy文件 (.npy)

1. **`*_x_coords.npy`**: X坐标数组 (n_traces,)
2. **`*_y_coords.npy`**: Y坐标数组 (n_traces,)  
3. **`*_time.npy`**: 时间轴数组 (n_samples,)
4. **`*_traces.npy`**: 地震道数据 (n_traces, n_samples)
5. **`*_xytz.npy`**: X-Y-Time-Amplitude展开数据 (n_traces×n_samples, 4)
6. **`*_info.npy`**: 基本信息字典

### NetCDF文件 (.nc)

包含以下变量：
- `amplitude(trace, time)`: 主要地震振幅数据
- `x_coordinate(trace)`: 每道的X坐标
- `y_coordinate(trace)`: 每道的Y坐标
- `time(time)`: 时间轴
- `x_expanded(point)`: 展开的X坐标
- `y_expanded(point)`: 展开的Y坐标
- `time_expanded(point)`: 展开的时间
- `amplitude_expanded(point)`: 展开的振幅

## 使用方法

### 基本用法

```bash
# 转换默认文件 mig01.sgy
python 03-sgync-simple.py

# 转换指定文件
python 03-sgync-simple.py your_file.sgy
```

### 在Python中使用

```python
from 03-sgync-simple import convert_to_xyz_format

# 转换文件
result = convert_to_xyz_format('mig01.sgy', output_dir='.')
print(f"转换完成: {result}")
```

## 数据格式说明

### X-Y-Time-Amplitude格式

`*_xytz.npy` 文件包含4列数据：
```
列1: X坐标 (米)
列2: Y坐标 (米)  
列3: 时间 (毫秒)
列4: 振幅值
```

每行代表一个数据点，总共有 `n_traces × n_samples` 行。

### 数据组织方式

- **原始格式**: (n_traces, n_samples) - 每道包含完整的时间序列
- **展开格式**: (n_traces×n_samples, 4) - 每个时间样点一行

## 示例输出

```
SGY到NetCDF转换工具 (X-Y-Time格式)
==================================================
正在读取SEG-Y文件: mig01.sgy
道数: 16972
采样点数: 1001
采样率: 1000 微秒
开始转换数据...
数据维度: (16972, 1001)
时间范围: 0.0 - 1000.0 ms
X坐标范围: 485000 - 515000
Y坐标范围: 4180000 - 4210000
创建X-Y-Time-Amplitude格式数据...
X-Y-Time-Amplitude数据保存完成: (16988972, 4) (每行: X, Y, Time, Amplitude)
正在创建NetCDF文件: mig01.nc
NetCDF文件创建完成: mig01.nc
转换完成！生成的文件:
  - mig01_x_coords.npy (0.1 MB)
  - mig01_y_coords.npy (0.1 MB)
  - mig01_time.npy (0.0 MB)
  - mig01_traces.npy (64.8 MB)
  - mig01_xytz.npy (259.2 MB)
  - mig01_info.npy (0.0 MB)
  - mig01.nc (45.9 MB)
```

## 读取转换后的数据

### 读取numpy文件

```python
import numpy as np

# 读取基本数据
x_coords = np.load('mig01_x_coords.npy')
y_coords = np.load('mig01_y_coords.npy')
time = np.load('mig01_time.npy')
traces = np.load('mig01_traces.npy')

# 读取X-Y-Time-Amplitude数据
xytz_data = np.load('mig01_xytz.npy')
x_expanded = xytz_data[:, 0]
y_expanded = xytz_data[:, 1]
time_expanded = xytz_data[:, 2]
amplitude_expanded = xytz_data[:, 3]

# 读取基本信息
info = np.load('mig01_info.npy', allow_pickle=True).item()
print(f"数据范围: X({info['x_min']}-{info['x_max']}), Y({info['y_min']}-{info['y_max']})")
```

### 读取NetCDF文件

```python
import netCDF4 as nc

with nc.Dataset('mig01.nc', 'r') as ncfile:
    # 读取主要数据
    amplitude = ncfile.variables['amplitude'][:]  # (n_traces, n_samples)
    x_coords = ncfile.variables['x_coordinate'][:]
    y_coords = ncfile.variables['y_coordinate'][:]
    time = ncfile.variables['time'][:]
    
    # 读取展开数据
    x_expanded = ncfile.variables['x_expanded'][:]
    y_expanded = ncfile.variables['y_expanded'][:]
    time_expanded = ncfile.variables['time_expanded'][:]
    amplitude_expanded = ncfile.variables['amplitude_expanded'][:]
    
    # 读取属性
    print(f"数据点总数: {ncfile.n_points}")
    print(f"坐标范围: X({ncfile.x_min}-{ncfile.x_max}), Y({ncfile.y_min}-{ncfile.y_max})")
```

## 测试工具

使用 `test_xyz_format.py` 验证转换结果：

```bash
python test_xyz_format.py
```

这个工具会：
- 检查所有生成的文件
- 显示数据统计信息
- 与原始格式进行比较
- 验证数据一致性

## 优势

1. **数据完整性**: 保持原始数据量，无信息丢失
2. **坐标精确性**: 使用真实的X、Y坐标，不依赖Inline/Crossline编号
3. **灵活性**: 支持不规则网格和任意坐标系
4. **兼容性**: 生成标准的NetCDF格式，符合CF约定
5. **效率**: 直接转换，无需复杂的插值计算

## 注意事项

1. **内存使用**: 展开格式会增加内存使用量（约4倍）
2. **文件大小**: `*_xytz.npy` 文件较大，包含所有数据点
3. **坐标系**: 保持原始SGY文件的坐标系，不进行坐标转换
4. **时间轴**: 使用原始的时间轴，单位为毫秒

## 依赖库

```bash
pip install segyio netcdf4 numpy
```

- `segyio`: 读取SEG-Y文件
- `netcdf4`: 创建NetCDF文件
- `numpy`: 数据处理
