#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试NetCDF文件的ArcGIS Pro兼容性
检查生成的NetCDF文件是否符合ArcGIS Pro的要求
"""

import numpy as np
import os

try:
    import netCDF4
    HAS_NETCDF4 = True
except ImportError:
    HAS_NETCDF4 = False
    print("错误: 需要安装netCDF4库")

def test_arcgis_compatibility(nc_file):
    """测试NetCDF文件的ArcGIS Pro兼容性"""
    print(f"测试ArcGIS Pro兼容性: {nc_file}")
    print("=" * 60)
    
    if not os.path.exists(nc_file):
        print(f"错误: 文件不存在: {nc_file}")
        return False
    
    if not HAS_NETCDF4:
        print("错误: 无法测试，需要安装netCDF4库")
        return False
    
    try:
        with netCDF4.Dataset(nc_file, 'r') as ncfile:
            print(f"✓ 文件可以正常打开")
            print(f"  格式: {ncfile.data_model}")
            print(f"  文件大小: {os.path.getsize(nc_file) / (1024*1024):.1f} MB")
            
            # 检查维度
            print(f"\n维度检查:")
            required_dims = ['x', 'y', 'z']
            for dim_name in required_dims:
                if dim_name in ncfile.dimensions:
                    print(f"  ✓ {dim_name}: {len(ncfile.dimensions[dim_name])}")
                else:
                    print(f"  ✗ 缺少维度: {dim_name}")
            
            if 'nv' in ncfile.dimensions:
                print(f"  ✓ nv: {len(ncfile.dimensions['nv'])} (边界变量)")
            else:
                print(f"  ⚠ 缺少nv维度 (边界变量)")
            
            # 检查坐标变量
            print(f"\n坐标变量检查:")
            coord_vars = ['x', 'y', 'z']
            for var_name in coord_vars:
                if var_name in ncfile.variables:
                    var = ncfile.variables[var_name]
                    print(f"  ✓ {var_name}: {var.shape} {var.dtype}")
                    
                    # 检查必要属性
                    attrs_to_check = ['standard_name', 'long_name', 'units', 'axis']
                    for attr in attrs_to_check:
                        if hasattr(var, attr):
                            print(f"    ✓ {attr}: {getattr(var, attr)}")
                        else:
                            print(f"    ⚠ 缺少属性: {attr}")
                    
                    # 检查边界属性
                    if hasattr(var, 'bounds'):
                        bounds_var = getattr(var, 'bounds')
                        if bounds_var in ncfile.variables:
                            print(f"    ✓ bounds: {bounds_var}")
                        else:
                            print(f"    ✗ 边界变量不存在: {bounds_var}")
                    else:
                        print(f"    ⚠ 缺少bounds属性")
                else:
                    print(f"  ✗ 缺少坐标变量: {var_name}")
            
            # 检查边界变量
            print(f"\n边界变量检查:")
            boundary_vars = ['x_bnds', 'y_bnds', 'z_bnds']
            for var_name in boundary_vars:
                if var_name in ncfile.variables:
                    var = ncfile.variables[var_name]
                    print(f"  ✓ {var_name}: {var.shape}")
                else:
                    print(f"  ⚠ 缺少边界变量: {var_name}")
            
            # 检查主数据变量
            print(f"\n主数据变量检查:")
            if 'amplitude' in ncfile.variables:
                amp_var = ncfile.variables['amplitude']
                print(f"  ✓ amplitude: {amp_var.shape} {amp_var.dtype}")
                
                # 检查维度顺序
                expected_dims = ('z', 'y', 'x')
                if amp_var.dimensions == expected_dims:
                    print(f"    ✓ 维度顺序正确: {amp_var.dimensions}")
                else:
                    print(f"    ⚠ 维度顺序: {amp_var.dimensions} (期望: {expected_dims})")
                
                # 检查重要属性
                important_attrs = ['standard_name', 'long_name', 'units', 'coordinates', 'grid_mapping']
                for attr in important_attrs:
                    if hasattr(amp_var, attr):
                        print(f"    ✓ {attr}: {getattr(amp_var, attr)}")
                    else:
                        print(f"    ⚠ 缺少属性: {attr}")
                
                # 检查数据范围
                try:
                    data_min = np.nanmin(amp_var[:])
                    data_max = np.nanmax(amp_var[:])
                    print(f"    ✓ 数据范围: {data_min:.3f} - {data_max:.3f}")
                    
                    # 检查填充值
                    if hasattr(amp_var, '_FillValue'):
                        fill_value = getattr(amp_var, '_FillValue')
                        fill_count = np.sum(amp_var[:] == fill_value)
                        total_count = amp_var.size
                        print(f"    ✓ 填充值: {fill_value} ({fill_count}/{total_count} = {fill_count/total_count*100:.1f}%)")
                    
                except Exception as e:
                    print(f"    ⚠ 无法读取数据: {e}")
            else:
                print(f"  ✗ 缺少主数据变量: amplitude")
            
            # 检查投影信息
            print(f"\n投影信息检查:")
            if 'crs' in ncfile.variables:
                crs_var = ncfile.variables['crs']
                print(f"  ✓ crs变量存在")
                
                # 检查投影属性
                proj_attrs = ['grid_mapping_name', 'spatial_ref', 'GeoTransform']
                for attr in proj_attrs:
                    if hasattr(crs_var, attr):
                        value = getattr(crs_var, attr)
                        if len(str(value)) > 100:
                            print(f"    ✓ {attr}: {str(value)[:100]}...")
                        else:
                            print(f"    ✓ {attr}: {value}")
                    else:
                        print(f"    ⚠ 缺少投影属性: {attr}")
            else:
                print(f"  ⚠ 缺少crs变量")
            
            # 检查全局属性
            print(f"\n全局属性检查:")
            important_global_attrs = ['Conventions', 'title', 'spatial_ref', 'GeoTransform']
            for attr in important_global_attrs:
                if hasattr(ncfile, attr):
                    value = getattr(ncfile, attr)
                    if len(str(value)) > 100:
                        print(f"  ✓ {attr}: {str(value)[:100]}...")
                    else:
                        print(f"  ✓ {attr}: {value}")
                else:
                    print(f"  ⚠ 缺少全局属性: {attr}")
            
            # 检查数据统计
            print(f"\n数据统计:")
            if hasattr(ncfile, 'n_x') and hasattr(ncfile, 'n_y') and hasattr(ncfile, 'n_z'):
                print(f"  网格大小: {ncfile.n_x} × {ncfile.n_y} × {ncfile.n_z}")
            
            if hasattr(ncfile, 'original_traces'):
                print(f"  原始道数: {ncfile.original_traces}")
            
            if hasattr(ncfile, 'data_coverage_percent'):
                print(f"  数据覆盖率: {ncfile.data_coverage_percent:.1f}%")
            
            print(f"\n✓ NetCDF文件基本符合ArcGIS Pro要求")
            return True
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_structure(nc_file):
    """测试文件结构"""
    print(f"\n详细文件结构:")
    print("=" * 40)
    
    try:
        with netCDF4.Dataset(nc_file, 'r') as ncfile:
            print("维度:")
            for name, dim in ncfile.dimensions.items():
                print(f"  {name}: {len(dim)}")
            
            print("\n变量:")
            for name, var in ncfile.variables.items():
                print(f"  {name}: {var.dimensions} {var.shape} {var.dtype}")
            
            print("\n全局属性:")
            for attr in ncfile.ncattrs():
                value = getattr(ncfile, attr)
                if len(str(value)) > 80:
                    print(f"  {attr}: {str(value)[:80]}...")
                else:
                    print(f"  {attr}: {value}")
                    
    except Exception as e:
        print(f"错误: {e}")

def main():
    """主函数"""
    print("ArcGIS Pro兼容性测试工具")
    print("=" * 60)
    
    # 测试默认文件
    nc_file = "mig01.nc"
    
    if os.path.exists(nc_file):
        success = test_arcgis_compatibility(nc_file)
        test_file_structure(nc_file)
        
        if success:
            print(f"\n🎉 测试通过！文件 {nc_file} 应该可以在ArcGIS Pro中正常打开")
            print(f"\n使用说明:")
            print(f"1. 在ArcGIS Pro中添加数据 -> NetCDF文件")
            print(f"2. 选择文件: {nc_file}")
            print(f"3. 选择变量: amplitude")
            print(f"4. 可以按时间(z)切片浏览3D数据")
        else:
            print(f"\n❌ 测试失败，文件可能无法在ArcGIS Pro中正常使用")
    else:
        print(f"错误: 文件不存在: {nc_file}")
        print("请先运行 03-sgync-simple.py 生成NetCDF文件")

if __name__ == "__main__":
    main()
