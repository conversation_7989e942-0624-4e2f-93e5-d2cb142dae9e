#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证修正后的NetCDF文件
检查单位和结构是否与point_to_nc_ui.py一致
"""

import netCDF4
import numpy as np
import os

def verify_meters_netcdf():
    """验证修正后的NetCDF文件"""
    nc_file = "mig01_meters.nc"
    
    if not os.path.exists(nc_file):
        print(f"文件不存在: {nc_file}")
        return
    
    print("验证修正后的NetCDF文件")
    print("=" * 50)
    
    try:
        with netCDF4.Dataset(nc_file, 'r') as ncfile:
            print(f"文件: {nc_file}")
            print(f"大小: {os.path.getsize(nc_file) / (1024*1024):.1f} MB")
            
            # 检查坐标变量的详细属性
            print(f"\n坐标变量详细检查:")
            
            # X坐标检查
            if 'x' in ncfile.variables:
                x_var = ncfile.variables['x']
                print(f"\nX坐标变量:")
                print(f"  形状: {x_var.shape}")
                print(f"  范围: {x_var[:].min():,.0f} - {x_var[:].max():,.0f}")
                print(f"  standard_name: {getattr(x_var, 'standard_name', '缺失')}")
                print(f"  long_name: {getattr(x_var, 'long_name', '缺失')}")
                print(f"  units: {getattr(x_var, 'units', '缺失')}")
                print(f"  axis: {getattr(x_var, 'axis', '缺失')}")
                print(f"  grid_mapping: {getattr(x_var, 'grid_mapping', '缺失')}")
            
            # Y坐标检查
            if 'y' in ncfile.variables:
                y_var = ncfile.variables['y']
                print(f"\nY坐标变量:")
                print(f"  形状: {y_var.shape}")
                print(f"  范围: {y_var[:].min():,.0f} - {y_var[:].max():,.0f}")
                print(f"  standard_name: {getattr(y_var, 'standard_name', '缺失')}")
                print(f"  long_name: {getattr(y_var, 'long_name', '缺失')}")
                print(f"  units: {getattr(y_var, 'units', '缺失')}")
                print(f"  axis: {getattr(y_var, 'axis', '缺失')}")
                print(f"  grid_mapping: {getattr(y_var, 'grid_mapping', '缺失')}")
            
            # Z坐标检查
            if 'z' in ncfile.variables:
                z_var = ncfile.variables['z']
                print(f"\nZ坐标变量:")
                print(f"  形状: {z_var.shape}")
                print(f"  范围: {z_var[:].min():.1f} - {z_var[:].max():.1f}")
                print(f"  standard_name: {getattr(z_var, 'standard_name', '缺失')}")
                print(f"  long_name: {getattr(z_var, 'long_name', '缺失')}")
                print(f"  units: {getattr(z_var, 'units', '缺失')}")
                print(f"  axis: {getattr(z_var, 'axis', '缺失')}")
                print(f"  positive: {getattr(z_var, 'positive', '缺失')}")
            
            # 检查与point_to_nc_ui.py的一致性
            print(f"\n与point_to_nc_ui.py一致性检查:")
            
            # 单位检查
            units_correct = True
            if 'x' in ncfile.variables:
                x_units = getattr(ncfile.variables['x'], 'units', '')
                if x_units != 'Meter':
                    print(f"  ⚠ X单位不正确: {x_units} (应该是 'Meter')")
                    units_correct = False
                else:
                    print(f"  ✓ X单位正确: {x_units}")
            
            if 'y' in ncfile.variables:
                y_units = getattr(ncfile.variables['y'], 'units', '')
                if y_units != 'Meter':
                    print(f"  ⚠ Y单位不正确: {y_units} (应该是 'Meter')")
                    units_correct = False
                else:
                    print(f"  ✓ Y单位正确: {y_units}")
            
            if 'z' in ncfile.variables:
                z_units = getattr(ncfile.variables['z'], 'units', '')
                if z_units != 'Meter':
                    print(f"  ⚠ Z单位不正确: {z_units} (应该是 'Meter')")
                    units_correct = False
                else:
                    print(f"  ✓ Z单位正确: {z_units}")
            
            # 标准名检查
            standard_names_correct = True
            if 'z' in ncfile.variables:
                z_standard = getattr(ncfile.variables['z'], 'standard_name', '')
                if z_standard != 'height':
                    print(f"  ⚠ Z标准名不正确: {z_standard} (应该是 'height')")
                    standard_names_correct = False
                else:
                    print(f"  ✓ Z标准名正确: {z_standard}")
            
            # positive属性检查
            positive_correct = True
            if 'z' in ncfile.variables:
                z_positive = getattr(ncfile.variables['z'], 'positive', '')
                if z_positive != 'up':
                    print(f"  ⚠ Z方向不正确: {z_positive} (应该是 'up')")
                    positive_correct = False
                else:
                    print(f"  ✓ Z方向正确: {z_positive}")
            
            # grid_mapping检查
            grid_mapping_correct = True
            for coord in ['x', 'y']:
                if coord in ncfile.variables:
                    gm = getattr(ncfile.variables[coord], 'grid_mapping', '')
                    if gm != 'projection':
                        print(f"  ⚠ {coord.upper()}的grid_mapping不正确: {gm} (应该是 'projection')")
                        grid_mapping_correct = False
                    else:
                        print(f"  ✓ {coord.upper()}的grid_mapping正确: {gm}")
            
            # 检查amplitude变量
            print(f"\n主数据变量检查:")
            if 'amplitude' in ncfile.variables:
                amp = ncfile.variables['amplitude']
                print(f"  amplitude: {amp.shape} {amp.dtype}")
                print(f"  维度顺序: {amp.dimensions}")
                print(f"  grid_mapping: {getattr(amp, 'grid_mapping', '缺失')}")
                print(f"  esri_pe_string: {'存在' if hasattr(amp, 'esri_pe_string') else '缺失'}")
                
                # 检查数据是否可读
                try:
                    sample = amp[0, 0, 0]
                    print(f"  ✓ 数据可读，样本值: {sample}")
                except Exception as e:
                    print(f"  ⚠ 数据读取失败: {e}")
            
            # 检查投影变量
            print(f"\n投影变量检查:")
            if 'projection' in ncfile.variables:
                proj = ncfile.variables['projection']
                print(f"  projection: {proj.shape} {proj.dtype}")
                
                # 检查关键投影参数
                proj_params = {
                    'grid_mapping_name': 'transverse_mercator',
                    'longitude_of_central_meridian': 111.0,
                    'false_easting': 500000.0,
                    'false_northing': 0.0
                }
                
                for param, expected in proj_params.items():
                    actual = getattr(proj, param, None)
                    if actual == expected:
                        print(f"    ✓ {param}: {actual}")
                    else:
                        print(f"    ⚠ {param}: {actual} (期望: {expected})")
            
            # 总体评估
            print(f"\n🎯 总体评估:")
            all_correct = (units_correct and standard_names_correct and 
                          positive_correct and grid_mapping_correct)
            
            if all_correct:
                print(f"  ✅ 完全符合point_to_nc_ui.py的结构标准")
                print(f"  ✅ 所有单位、标准名、属性都正确")
                print(f"  ✅ 应该可以在ArcGIS Pro中正常渲染")
            else:
                print(f"  ⚠ 仍有部分属性需要调整")
            
            print(f"\n📊 数据信息:")
            if hasattr(ncfile, 'data_coverage_percent'):
                print(f"  数据覆盖率: {ncfile.data_coverage_percent:.1f}%")
            if hasattr(ncfile, 'coordinate_system'):
                print(f"  坐标系统: {ncfile.coordinate_system}")
            
    except Exception as e:
        print(f"验证失败: {e}")
        import traceback
        traceback.print_exc()

def compare_with_point_to_nc():
    """与point_to_nc_ui.py的标准进行对比"""
    print(f"\n与point_to_nc_ui.py标准对比:")
    print("=" * 40)
    
    print("期望的属性设置:")
    print("X坐标:")
    print("  standard_name: 'projection_x_coordinate'")
    print("  long_name: 'x coordinate of projection'")
    print("  units: 'Meter'")
    print("  grid_mapping: 'projection'")
    print("  axis: 'X'")
    
    print("\nY坐标:")
    print("  standard_name: 'projection_y_coordinate'")
    print("  long_name: 'y coordinate of projection'")
    print("  units: 'Meter'")
    print("  grid_mapping: 'projection'")
    print("  axis: 'Y'")
    
    print("\nZ坐标:")
    print("  standard_name: 'height'")
    print("  long_name: 'height above reference level'")
    print("  units: 'Meter'")
    print("  axis: 'Z'")
    print("  positive: 'up'")

if __name__ == "__main__":
    verify_meters_netcdf()
    compare_with_point_to_nc()
