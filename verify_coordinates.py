#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证NetCDF文件中的坐标是否正确
"""

import netCDF4
import numpy as np
import os

def verify_coordinates():
    """验证坐标"""
    nc_file = "mig01_corrected.nc"
    
    if not os.path.exists(nc_file):
        print(f"文件不存在: {nc_file}")
        return
    
    print("验证NetCDF文件坐标")
    print("=" * 40)
    
    try:
        with netCDF4.Dataset(nc_file, 'r') as ncfile:
            # 读取坐标
            x = ncfile.variables['x'][:]
            y = ncfile.variables['y'][:]
            z = ncfile.variables['z'][:]
            
            print(f"X坐标范围: {x.min():.1f} - {x.max():.1f}")
            print(f"Y坐标范围: {y.min():.1f} - {y.max():.1f}")
            print(f"Z(时间)范围: {z.min():.1f} - {z.max():.1f} ms")
            
            print(f"\n网格信息:")
            print(f"X网格点数: {len(x)}")
            print(f"Y网格点数: {len(y)}")
            print(f"Z网格点数: {len(z)}")
            
            print(f"\n网格分辨率:")
            if len(x) > 1:
                x_res = x[1] - x[0]
                print(f"X分辨率: {x_res:.1f} 米")
            if len(y) > 1:
                y_res = y[1] - y[0]
                print(f"Y分辨率: {y_res:.1f} 米")
            if len(z) > 1:
                z_res = z[1] - z[0]
                print(f"时间分辨率: {z_res:.1f} ms")
            
            print(f"\n期望的坐标范围 (加上37000000后):")
            print(f"X: 37,517,884 - 37,519,174 (实际: {x.min():.0f} - {x.max():.0f})")
            print(f"Y: 4,842,597 - 4,844,453 (实际: {y.min():.0f} - {y.max():.0f})")

            # 检查坐标是否在合理范围内
            x_ok = 37517000 <= x.min() <= 37520000 and 37517000 <= x.max() <= 37520000
            y_ok = 4840000 <= y.min() <= 4850000 and 4840000 <= y.max() <= 4850000
            
            print(f"\n坐标验证:")
            print(f"X坐标: {'✓ 正确' if x_ok else '✗ 错误'}")
            print(f"Y坐标: {'✓ 正确' if y_ok else '✗ 错误'}")
            
            if x_ok and y_ok:
                print(f"\n🎉 坐标验证通过！现在可以在ArcGIS Pro中正确显示")
            else:
                print(f"\n❌ 坐标验证失败，需要进一步调整")
                
    except Exception as e:
        print(f"错误: {e}")

def verify_numpy_coordinates():
    """验证numpy文件中的坐标"""
    print(f"\n验证numpy文件坐标:")
    print("=" * 30)
    
    try:
        x_coords = np.load('mig01_x_coords.npy')
        y_coords = np.load('mig01_y_coords.npy')
        
        print(f"原始道数据坐标:")
        print(f"X: {x_coords.min():.1f} - {x_coords.max():.1f} ({len(x_coords)} 道)")
        print(f"Y: {y_coords.min():.1f} - {y_coords.max():.1f} ({len(y_coords)} 道)")
        
        # 显示前几个坐标点
        print(f"\n前5个坐标点:")
        for i in range(min(5, len(x_coords))):
            print(f"  道 {i+1}: X={x_coords[i]:.1f}, Y={y_coords[i]:.1f}")
            
    except Exception as e:
        print(f"读取numpy文件失败: {e}")

if __name__ == "__main__":
    verify_coordinates()
    verify_numpy_coordinates()
