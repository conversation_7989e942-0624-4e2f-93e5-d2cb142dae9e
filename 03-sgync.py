#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SGY到NetCDF转换工具
将SEG-Y地震数据转换为NetCDF格式，便于科学计算和数据分析
"""

import segyio
import numpy as np
import os
import sys
from datetime import datetime

# 尝试导入NetCDF4库
try:
    import netCDF4
    HAS_NETCDF4 = True
except ImportError:
    HAS_NETCDF4 = False
    print("警告: 未安装netCDF4库，将只保存numpy格式文件")
    print("安装命令: pip install netcdf4")

# 尝试导入xarray库
try:
    import xarray as xr
    HAS_XARRAY = True
except ImportError:
    HAS_XARRAY = False

# 尝试导入scipy库
try:
    from scipy.spatial import cKDTree
    HAS_SCIPY = True
except ImportError:
    HAS_SCIPY = False
    print("警告: 未安装scipy库，将使用简单的最近邻插值")
    print("安装命令: pip install scipy")


class SGYReader:
    """SEG-Y文件读取器"""

    def __init__(self, filename):
        self.filename = filename
        self.segy_file = None
        self.traces = None
        self.headers = None
        self.binary_header = None

    def read(self):
        """读取SEG-Y文件"""
        print(f"正在读取SEG-Y文件: {self.filename}")

        with segyio.open(self.filename, strict=False) as segy:
            # 获取基本信息
            print(f"道数: {segy.tracecount}")
            print(f"采样点数: {len(segy.samples)}")
            print(f"采样率: {segy.bin[segyio.BinField.Interval]} 微秒")

            # 读取二进制文件头
            self.binary_header = dict(segy.bin)

            # 读取道头信息
            inlines = [segy.header[i][9] for i in range(segy.tracecount)]
            crosslines = [segy.header[i][21] for i in range(segy.tracecount)]
            x_coords = [segy.header[i][73] for i in range(segy.tracecount)]
            y_coords = [segy.header[i][77] for i in range(segy.tracecount)]

            # 转换为numpy数组
            inlines = np.array(inlines)
            crosslines = np.array(crosslines)
            x_coords = np.array(x_coords)
            y_coords = np.array(y_coords)

            # 获取唯一的inline和crossline值
            unique_inlines = np.unique(inlines)
            unique_crosslines = np.unique(crosslines)

            print(f"Inline范围: {min(unique_inlines)} - {max(unique_inlines)}")
            print(f"Crossline范围: {min(unique_crosslines)} - {max(unique_crosslines)}")
            print(f"Inline数量: {len(unique_inlines)}")
            print(f"Crossline数量: {len(unique_crosslines)}")

            # 读取地震数据
            seismic_data = np.stack([t.astype(float) for t in segy.trace])

            self.traces = {
                'data': seismic_data,
                'inlines': inlines,
                'crosslines': crosslines,
                'x_coords': x_coords,
                'y_coords': y_coords,
                'unique_inlines': unique_inlines,
                'unique_crosslines': unique_crosslines,
                'samples': segy.samples,
                'dt': segy.bin[segyio.BinField.Interval]
            }

            # 读取所有道头
            self.headers = []
            for i in range(segy.tracecount):
                header = dict(segy.header[i])
                self.headers.append(header)

        return self.traces


class SGYToNCConverter:
    """SGY到NetCDF转换器"""

    def __init__(self):
        self.reader = None

    def convert(self, sgy_file, nc_file, include_headers=True):
        """转换SGY到NetCDF"""
        # 读取SGY文件
        self.reader = SGYReader(sgy_file)
        segy_data = self.reader.read()

        # 获取基本信息
        traces = segy_data['data']
        inlines = segy_data['inlines']
        crosslines = segy_data['crosslines']
        x_coords = segy_data['x_coords']
        y_coords = segy_data['y_coords']
        unique_inlines = segy_data['unique_inlines']
        unique_crosslines = segy_data['unique_crosslines']
        samples = segy_data['samples']
        dt = segy_data['dt']

        n_traces, n_samples = traces.shape
        sample_interval = dt / 1000.0  # 转换为毫秒
        time = samples  # 使用原始时间轴

        # 获取输出文件的基本名称
        base_name = os.path.splitext(os.path.basename(nc_file))[0]
        output_dir = os.path.dirname(nc_file) or '.'

        print(f"开始转换数据...")
        print(f"数据维度: {traces.shape}")
        print(f"时间范围: {time[0]:.1f} - {time[-1]:.1f} ms")

        # 创建基于X、Y、Time的数据格式（保持原始数据量）
        print(f"创建X-Y-Time格式的数据（保持原始数据量）...")

        # 获取X、Y坐标的范围
        x_min, x_max = x_coords.min(), x_coords.max()
        y_min, y_max = y_coords.min(), y_coords.max()

        print(f"X坐标范围: {x_min} - {x_max}")
        print(f"Y坐标范围: {y_min} - {y_max}")
        print(f"原始数据量: {n_traces} 道 × {n_samples} 时间样点")

        # 直接保存原始数据，不进行插值或重采样
        # 保存X、Y坐标和时间轴
        np.save(os.path.join(output_dir, f'{base_name}_x_coords.npy'), x_coords)
        np.save(os.path.join(output_dir, f'{base_name}_y_coords.npy'), y_coords)
        np.save(os.path.join(output_dir, f'{base_name}_time.npy'), time)

        # 保存地震道数据 (traces × time)
        np.save(os.path.join(output_dir, f'{base_name}_traces.npy'), traces)

        # 保存原始trace数据（X, Y, Time, Amplitude格式）
        # 创建X-Y-Time-Amplitude的4列数据
        print("创建X-Y-Time-Amplitude格式数据...")

        # 为每个时间样点创建X, Y, Time, Amplitude的记录
        x_expanded = np.repeat(x_coords, n_samples)
        y_expanded = np.repeat(y_coords, n_samples)
        time_expanded = np.tile(time, n_traces)
        amplitude_expanded = traces.flatten()

        # 组合成4列数据: [X, Y, Time, Amplitude]
        trace_data = np.column_stack((x_expanded, y_expanded, time_expanded, amplitude_expanded))

        np.save(os.path.join(output_dir, f'{base_name}_xytz.npy'), trace_data)
        print(f"X-Y-Time-Amplitude数据保存完成: {trace_data.shape} (每行: X, Y, Time, Amplitude)")

        # 保存基本信息
        grid_info = {
            'x_min': x_min,
            'x_max': x_max,
            'y_min': y_min,
            'y_max': y_max,
            'sample_interval': sample_interval,
            'n_samples': n_samples,
            'n_traces': n_traces,
            'time_start': time[0],
            'time_end': time[-1]
        }
        np.save(os.path.join(output_dir, f'{base_name}_info.npy'), grid_info)

        # 如果有netCDF4库，创建NetCDF文件
        if HAS_NETCDF4:
            self._create_netcdf_xyz(nc_file, x_coords, y_coords, time, traces,
                                  trace_data, base_name, sample_interval, include_headers)
        else:
            # 创建简单的文本描述文件
            with open(nc_file.replace('.nc', '_info.txt'), 'w', encoding='utf-8') as f:
                f.write(f"SGY转换信息 (X-Y-Time格式)\n")
                f.write(f"源文件: {sgy_file}\n")
                f.write(f"转换时间: {datetime.now()}\n")
                f.write(f"数据维度: {traces.shape}\n")
                f.write(f"坐标范围: X({x_min}-{x_max}), Y({y_min}-{y_max})\n")
                f.write(f"生成的文件:\n")
                f.write(f"  - {base_name}_traces.npy: 地震道数据 (traces × time)\n")
                f.write(f"  - {base_name}_x_coords.npy: X坐标\n")
                f.write(f"  - {base_name}_y_coords.npy: Y坐标\n")
                f.write(f"  - {base_name}_time.npy: 时间轴\n")
                f.write(f"  - {base_name}_xytz.npy: X-Y-Time-Amplitude数据\n")
                f.write(f"  - {base_name}_info.npy: 基本信息\n")

        print(f"X-Y-Time格式数据保存完成: {n_traces} 道 × {n_samples} 时间样点")

        # 同时保存传统的Inline-Crossline格式（向后兼容）
        try:
            n_il = len(unique_inlines)
            n_xl = len(unique_crosslines)

            print(f"同时保存Inline-Crossline格式: ({n_il}, {n_xl}, {n_samples})")

            # 创建3D数据立方体 (inline, crossline, time)
            data_cube_il = np.full((n_il, n_xl, n_samples), np.nan, dtype=np.float32)

            # 填充数据
            for i, (il, xl) in enumerate(zip(inlines, crosslines)):
                il_idx = np.where(unique_inlines == il)[0]
                xl_idx = np.where(unique_crosslines == xl)[0]
                if len(il_idx) > 0 and len(xl_idx) > 0:
                    data_cube_il[il_idx[0], xl_idx[0], :] = traces[i, :]

            # 保存传统格式
            np.save(os.path.join(output_dir, f'{base_name}_data_inline.npy'), data_cube_il)
            np.save(os.path.join(output_dir, f'{base_name}_inlines.npy'), unique_inlines)
            np.save(os.path.join(output_dir, f'{base_name}_crosslines.npy'), unique_crosslines)

        except Exception as e:
            print(f"保存Inline-Crossline格式时出错: {e}")

        return nc_file

    def _create_netcdf_xyz(self, nc_file, x_coords, y_coords, time, traces,
                          trace_data, base_name, sample_interval, include_headers):
        """创建基于X-Y-Time的NetCDF文件"""
        print(f"正在创建X-Y-Time格式的NetCDF文件: {nc_file}")

        with netCDF4.Dataset(nc_file, 'w', format='NETCDF4') as ncfile:
            n_traces, n_samples = traces.shape
            n_points = trace_data.shape[0]  # X-Y-Time-Amplitude数据点总数

            # 创建维度
            ncfile.createDimension('trace', n_traces)
            ncfile.createDimension('time', n_samples)
            ncfile.createDimension('point', n_points)

            # 创建坐标变量
            trace_var = ncfile.createVariable('trace', 'i4', ('trace',))
            trace_var[:] = np.arange(n_traces)
            trace_var.long_name = 'Trace number'

            time_var = ncfile.createVariable('time', 'f4', ('time',))
            time_var[:] = time
            time_var.long_name = 'Time'
            time_var.units = 'milliseconds'
            time_var.standard_name = 'time'

            # 创建X、Y坐标变量（每道一个坐标）
            x_var = ncfile.createVariable('x_coordinate', 'f4', ('trace',))
            x_var[:] = x_coords
            x_var.long_name = 'X coordinate'
            x_var.units = 'meters'

            y_var = ncfile.createVariable('y_coordinate', 'f4', ('trace',))
            y_var[:] = y_coords
            y_var.long_name = 'Y coordinate'
            y_var.units = 'meters'

            # 创建主数据变量 (trace, time)
            amplitude_var = ncfile.createVariable('amplitude', 'f4', ('trace', 'time'),
                                                 zlib=True, complevel=6)
            amplitude_var[:] = traces
            amplitude_var.long_name = 'Seismic amplitude'
            amplitude_var.units = 'dimensionless'
            amplitude_var.coordinates = 'x_coordinate y_coordinate'

            # 创建展开的X-Y-Time-Amplitude数据
            x_expanded_var = ncfile.createVariable('x_expanded', 'f4', ('point',))
            x_expanded_var[:] = trace_data[:, 0]
            x_expanded_var.long_name = 'X coordinate (expanded)'
            x_expanded_var.units = 'meters'

            y_expanded_var = ncfile.createVariable('y_expanded', 'f4', ('point',))
            y_expanded_var[:] = trace_data[:, 1]
            y_expanded_var.long_name = 'Y coordinate (expanded)'
            y_expanded_var.units = 'meters'

            time_expanded_var = ncfile.createVariable('time_expanded', 'f4', ('point',))
            time_expanded_var[:] = trace_data[:, 2]
            time_expanded_var.long_name = 'Time (expanded)'
            time_expanded_var.units = 'milliseconds'

            amplitude_expanded_var = ncfile.createVariable('amplitude_expanded', 'f4', ('point',))
            amplitude_expanded_var[:] = trace_data[:, 3]
            amplitude_expanded_var.long_name = 'Seismic amplitude (expanded)'
            amplitude_expanded_var.units = 'dimensionless'

            # 添加全局属性
            ncfile.source_file = os.path.basename(self.reader.filename)
            ncfile.sample_interval_ms = float(sample_interval)
            ncfile.n_samples = int(n_samples)
            ncfile.n_traces = int(n_traces)
            ncfile.n_points = int(n_points)
            ncfile.data_format = int(self.reader.binary_header.get('data_format', 1))
            ncfile.created_by = 'SGY to NetCDF Converter v1.0 (X-Y-Time format)'
            ncfile.creation_date = datetime.now().isoformat()
            ncfile.conventions = 'CF-1.6'
            ncfile.title = f'Seismic data converted from {os.path.basename(self.reader.filename)} (X-Y-Time format)'
            ncfile.description = 'Seismic data in X-Y-Time format preserving original trace count'

            # 添加坐标范围信息
            ncfile.x_min = float(x_coords.min())
            ncfile.x_max = float(x_coords.max())
            ncfile.y_min = float(y_coords.min())
            ncfile.y_max = float(y_coords.max())

            # 添加道头信息（如果需要）
            if include_headers and self.reader.headers:
                self._add_trace_headers_xyz(ncfile, n_traces)

        print(f"X-Y-Time格式NetCDF文件创建完成: {nc_file}")

    def _create_netcdf_3d(self, nc_file, data_cube, inlines, crosslines, time,
                         x_grid, y_grid, base_name, sample_interval, include_headers):
        """创建3D NetCDF文件"""
        print(f"正在创建NetCDF文件: {nc_file}")

        with netCDF4.Dataset(nc_file, 'w', format='NETCDF4') as ncfile:
            n_il, n_xl, n_samples = data_cube.shape

            # 创建维度
            ncfile.createDimension('inline', n_il)
            ncfile.createDimension('crossline', n_xl)
            ncfile.createDimension('time', n_samples)

            # 创建坐标变量
            inline_var = ncfile.createVariable('inline', 'i4', ('inline',))
            inline_var[:] = inlines
            inline_var.long_name = 'Inline number'
            inline_var.units = 'dimensionless'

            crossline_var = ncfile.createVariable('crossline', 'i4', ('crossline',))
            crossline_var[:] = crosslines
            crossline_var.long_name = 'Crossline number'
            crossline_var.units = 'dimensionless'

            time_var = ncfile.createVariable('time', 'f4', ('time',))
            time_var[:] = time
            time_var.long_name = 'Time'
            time_var.units = 'milliseconds'
            time_var.standard_name = 'time'

            # 创建坐标网格变量
            x_var = ncfile.createVariable('x_coordinate', 'f4', ('inline', 'crossline'))
            x_var[:] = x_grid
            x_var.long_name = 'X coordinate'
            x_var.units = 'meters'

            y_var = ncfile.createVariable('y_coordinate', 'f4', ('inline', 'crossline'))
            y_var[:] = y_grid
            y_var.long_name = 'Y coordinate'
            y_var.units = 'meters'

            # 创建主数据变量
            amplitude_var = ncfile.createVariable('amplitude', 'f4', ('inline', 'crossline', 'time'),
                                                 fill_value=np.nan, zlib=True, complevel=6)
            amplitude_var[:] = data_cube
            amplitude_var.long_name = 'Seismic amplitude'
            amplitude_var.units = 'dimensionless'
            amplitude_var.coordinates = 'x_coordinate y_coordinate'

            # 添加全局属性
            ncfile.source_file = os.path.basename(self.reader.filename)
            ncfile.sample_interval_ms = float(sample_interval)
            ncfile.n_samples = int(n_samples)
            ncfile.n_inlines = int(n_il)
            ncfile.n_crosslines = int(n_xl)
            ncfile.data_format = int(self.reader.binary_header.get('data_format', 1))
            ncfile.created_by = 'SGY to NetCDF Converter v1.0'
            ncfile.creation_date = datetime.now().isoformat()
            ncfile.conventions = 'CF-1.6'
            ncfile.title = f'Seismic data converted from {os.path.basename(self.reader.filename)}'

            # 添加道头信息（如果需要）
            if include_headers and self.reader.headers:
                self._add_trace_headers_3d(ncfile, inlines, crosslines)

        print(f"NetCDF文件创建完成: {nc_file}")

    def _create_netcdf_2d(self, nc_file, traces, inlines, crosslines, time,
                         x_coords, y_coords, base_name, sample_interval, include_headers):
        """创建2D NetCDF文件"""
        print(f"正在创建2D NetCDF文件: {nc_file}")

        with netCDF4.Dataset(nc_file, 'w', format='NETCDF4') as ncfile:
            n_traces, n_samples = traces.shape

            # 创建维度
            ncfile.createDimension('trace', n_traces)
            ncfile.createDimension('time', n_samples)

            # 创建坐标变量
            trace_var = ncfile.createVariable('trace', 'i4', ('trace',))
            trace_var[:] = np.arange(n_traces)
            trace_var.long_name = 'Trace number'

            time_var = ncfile.createVariable('time', 'f4', ('time',))
            time_var[:] = time
            time_var.long_name = 'Time'
            time_var.units = 'milliseconds'
            time_var.standard_name = 'time'

            # 创建道头信息变量
            inline_var = ncfile.createVariable('inline', 'i4', ('trace',))
            inline_var[:] = inlines
            inline_var.long_name = 'Inline number'

            crossline_var = ncfile.createVariable('crossline', 'i4', ('trace',))
            crossline_var[:] = crosslines
            crossline_var.long_name = 'Crossline number'

            x_var = ncfile.createVariable('x_coordinate', 'f4', ('trace',))
            x_var[:] = x_coords
            x_var.long_name = 'X coordinate'
            x_var.units = 'meters'

            y_var = ncfile.createVariable('y_coordinate', 'f4', ('trace',))
            y_var[:] = y_coords
            y_var.long_name = 'Y coordinate'
            y_var.units = 'meters'

            # 创建主数据变量
            amplitude_var = ncfile.createVariable('amplitude', 'f4', ('trace', 'time'),
                                                 zlib=True, complevel=6)
            amplitude_var[:] = traces
            amplitude_var.long_name = 'Seismic amplitude'
            amplitude_var.units = 'dimensionless'
            amplitude_var.coordinates = 'x_coordinate y_coordinate'

            # 添加全局属性
            ncfile.source_file = os.path.basename(self.reader.filename)
            ncfile.sample_interval_ms = float(sample_interval)
            ncfile.n_samples = int(n_samples)
            ncfile.n_traces = int(n_traces)
            ncfile.data_format = int(self.reader.binary_header.get('data_format', 1))
            ncfile.created_by = 'SGY to NetCDF Converter v1.0'
            ncfile.creation_date = datetime.now().isoformat()
            ncfile.conventions = 'CF-1.6'
            ncfile.title = f'Seismic data converted from {os.path.basename(self.reader.filename)}'

            # 添加道头信息（如果需要）
            if include_headers and self.reader.headers:
                self._add_trace_headers_2d(ncfile, n_traces)

        print(f"2D NetCDF文件创建完成: {nc_file}")

    def _add_trace_headers_3d(self, ncfile, inlines, crosslines):
        """添加3D格式的道头信息"""
        try:
            # 创建道头组
            header_group = ncfile.createGroup('trace_headers')

            # 选择一些重要的道头字段
            important_headers = [1, 5, 9, 17, 21, 37, 41, 73, 77, 115, 117]
            header_names = ['trace_seq', 'energy_source_point', 'inline', 'water_depth',
                          'crossline', 'source_x', 'source_y', 'group_x', 'group_y',
                          'sample_interval', 'n_samples']

            n_il, n_xl = len(inlines), len(crosslines)

            for header_byte, header_name in zip(important_headers, header_names):
                if header_byte in [9, 21]:  # inline, crossline 已经有了
                    continue

                # 创建2D数组存储道头信息
                header_data = np.full((n_il, n_xl), np.nan)

                # 填充道头数据
                for i, header in enumerate(self.reader.headers):
                    il = header.get(9, 0)
                    xl = header.get(21, 0)
                    il_idx = np.where(inlines == il)[0]
                    xl_idx = np.where(crosslines == xl)[0]
                    if len(il_idx) > 0 and len(xl_idx) > 0:
                        header_data[il_idx[0], xl_idx[0]] = header.get(header_byte, 0)

                var = header_group.createVariable(header_name, 'f4', ('inline', 'crossline'))
                var[:] = header_data
                var.description = f'Trace header byte {header_byte}'

        except Exception as e:
            print(f"添加道头信息时出错: {e}")

    def _add_trace_headers_2d(self, ncfile, n_traces):
        """添加2D格式的道头信息"""
        try:
            # 创建道头组
            header_group = ncfile.createGroup('trace_headers')

            # 选择一些重要的道头字段
            important_headers = [1, 5, 9, 17, 21, 37, 41, 73, 77, 115, 117]
            header_names = ['trace_seq', 'energy_source_point', 'inline', 'water_depth',
                          'crossline', 'source_x', 'source_y', 'group_x', 'group_y',
                          'sample_interval', 'n_samples']

            for header_byte, header_name in zip(important_headers, header_names):
                header_data = np.zeros(n_traces)

                for i, header in enumerate(self.reader.headers[:n_traces]):
                    header_data[i] = header.get(header_byte, 0)

                var = header_group.createVariable(header_name, 'f4', ('trace',))
                var[:] = header_data
                var.description = f'Trace header byte {header_byte}'

        except Exception as e:
            print(f"添加道头信息时出错: {e}")

    def _add_trace_headers_xyz(self, ncfile, n_traces):
        """添加X-Y-Time格式的道头信息"""
        try:
            # 创建道头组
            header_group = ncfile.createGroup('trace_headers')

            # 选择一些重要的道头字段
            important_headers = [1, 5, 9, 17, 21, 37, 41, 73, 77, 115, 117]
            header_names = ['trace_seq', 'energy_source_point', 'inline', 'water_depth',
                          'crossline', 'source_x', 'source_y', 'group_x', 'group_y',
                          'sample_interval', 'n_samples']

            for header_byte, header_name in zip(important_headers, header_names):
                header_data = np.zeros(n_traces)

                for i, header in enumerate(self.reader.headers[:n_traces]):
                    header_data[i] = header.get(header_byte, 0)

                var = header_group.createVariable(header_name, 'f4', ('trace',))
                var[:] = header_data
                var.description = f'Trace header byte {header_byte}'

        except Exception as e:
            print(f"添加X-Y-Time格式道头信息时出错: {e}")


def convert_sgy_to_nc(sgy_file, nc_file=None, include_headers=True):
    """
    便捷函数：将SGY文件转换为NetCDF格式

    参数:
    - sgy_file: 输入的SGY文件路径
    - nc_file: 输出的NC文件路径（可选，默认与SGY文件同名）
    - include_headers: 是否包含道头信息

    返回:
    - 输出文件路径
    """
    if nc_file is None:
        base_name = os.path.splitext(sgy_file)[0]
        nc_file = f"{base_name}.nc"

    converter = SGYToNCConverter()
    return converter.convert(sgy_file, nc_file, include_headers)


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='SGY到NetCDF转换工具')
    parser.add_argument('input_file', nargs='?', help='输入的SGY文件路径')
    parser.add_argument('-o', '--output', help='输出的NC文件路径（可选）')
    parser.add_argument('--no-headers', action='store_true', help='不包含道头信息')
    parser.add_argument('--check-deps', action='store_true', help='检查依赖库')

    args = parser.parse_args()

    # 检查依赖库
    if args.check_deps:
        print("检查依赖库...")
        try:
            import segyio
            version = getattr(segyio, '__version__', '未知版本')
            print(f"segyio: 已安装 (版本: {version})")
        except ImportError:
            print("segyio: 未安装")
        print(f"netCDF4: {'已安装' if HAS_NETCDF4 else '未安装'}")
        print(f"xarray: {'已安装' if HAS_XARRAY else '未安装'}")
        return 0

    # 检查输入文件
    if not args.input_file:
        print("错误: 请提供输入文件路径")
        parser.print_help()
        return 1

    if not os.path.exists(args.input_file):
        print(f"错误: 输入文件不存在: {args.input_file}")
        return 1

    # 设置输出文件
    output_file = args.output
    if output_file is None:
        base_name = os.path.splitext(args.input_file)[0]
        output_file = f"{base_name}.nc"

    try:
        print(f"开始转换: {args.input_file} -> {output_file}")

        # 执行转换
        result = convert_sgy_to_nc(
            args.input_file,
            output_file,
            include_headers=not args.no_headers
        )

        print(f"转换完成: {result}")

        # 显示生成的文件
        output_dir = os.path.dirname(output_file) or '.'
        base_name = os.path.splitext(os.path.basename(output_file))[0]

        print("\n生成的文件:")
        for ext in ['_data.npy', '_inlines.npy', '_crosslines.npy', '_x_coords.npy', '_y_coords.npy', '_grid.npy']:
            file_path = os.path.join(output_dir, f"{base_name}{ext}")
            if os.path.exists(file_path):
                size = os.path.getsize(file_path) / (1024*1024)  # MB
                print(f"  - {os.path.basename(file_path)} ({size:.1f} MB)")

        if os.path.exists(output_file):
            size = os.path.getsize(output_file) / (1024*1024)  # MB
            print(f"  - {os.path.basename(output_file)} ({size:.1f} MB)")

        return 0

    except Exception as e:
        print(f"转换失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    # 如果直接运行，使用默认参数转换mig01.sgy
    if len(sys.argv) == 1:
        print("SGY到NetCDF转换工具")
        print("=" * 50)

        # 检查是否存在mig01.sgy
        default_file = "mig01.sgy"
        if os.path.exists(default_file):
            print(f"发现默认文件: {default_file}")
            print("开始转换...")

            try:
                result = convert_sgy_to_nc(default_file, "mig01.nc", include_headers=True)
                print(f"\n转换完成!")
                print(f"输出文件: {result}")

                # 显示生成的文件信息
                print("\n生成的文件:")
                for filename in os.listdir('.'):
                    if filename.startswith('mig01') and (filename.endswith('.npy') or filename.endswith('.nc')):
                        size = os.path.getsize(filename) / (1024*1024)  # MB
                        print(f"  - {filename} ({size:.1f} MB)")

            except Exception as e:
                print(f"转换失败: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"未找到默认文件: {default_file}")
            print("\n使用方法:")
            print("  python 03-sgync.py <sgy_file> [-o output.nc] [--no-headers]")
            print("\n示例:")
            print("  python 03-sgync.py mig01.sgy")
            print("  python 03-sgync.py data.sgy -o converted.nc")
            print("  python 03-sgync.py data.sgy --no-headers")
            print("  python 03-sgync.py --check-deps  # 检查依赖库")
    else:
        # 使用命令行参数
        exit_code = main()
        sys.exit(exit_code)