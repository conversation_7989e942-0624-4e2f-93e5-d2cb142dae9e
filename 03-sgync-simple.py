#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SGY到NetCDF转换工具 - 简化版本
将SEG-Y地震数据转换为X-Y-Time格式的NetCDF
"""

import segyio
import numpy as np
import os
import sys
from datetime import datetime

# 尝试导入NetCDF4库
try:
    import netCDF4
    HAS_NETCDF4 = True
except ImportError:
    HAS_NETCDF4 = False
    print("警告: 未安装netCDF4库，将只保存numpy格式文件")

def read_sgy_file(filename):
    """读取SEG-Y文件并提取数据和道头信息"""
    print(f"正在读取SEG-Y文件: {filename}")
    
    with segyio.open(filename, strict=False) as segy:
        # 获取基本信息
        print(f"道数: {segy.tracecount}")
        print(f"采样点数: {len(segy.samples)}")
        print(f"采样率: {segy.bin[segyio.BinField.Interval]} 微秒")
        
        # 读取道头信息
        inlines = [segy.header[i][9] for i in range(segy.tracecount)]
        crosslines = [segy.header[i][21] for i in range(segy.tracecount)]
        x_coords = [segy.header[i][73] for i in range(segy.tracecount)]  # 道头73是X坐标
        y_coords = [segy.header[i][77] for i in range(segy.tracecount)]  # 道头77是Y坐标

        # 转换为numpy数组
        inlines = np.array(inlines)
        crosslines = np.array(crosslines)
        x_coords = np.array(x_coords)
        y_coords = np.array(y_coords)

        # 修正坐标 - 根据图片显示的正确坐标进行调整
        print(f"原始X坐标范围: {x_coords.min()} - {x_coords.max()}")
        print(f"原始Y坐标范围: {y_coords.min()} - {y_coords.max()}")

        # 坐标看起来被放大了10倍，需要除以10
        x_coords = x_coords / 10.0
        y_coords = y_coords / 10.0

        print(f"除以10后X坐标范围: {x_coords.min()} - {x_coords.max()}")
        print(f"除以10后Y坐标范围: {y_coords.min()} - {y_coords.max()}")

        # 给X坐标添加37000000（带号偏移）
        x_coords = x_coords + 37000000.0

        print(f"最终X坐标范围: {x_coords.min()} - {x_coords.max()}")
        print(f"最终Y坐标范围: {y_coords.min()} - {y_coords.max()}")
        
        # 读取地震数据
        seismic_data = np.stack([t.astype(float) for t in segy.trace])
        
        return {
            'data': seismic_data,
            'inlines': inlines,
            'crosslines': crosslines,
            'x_coords': x_coords,
            'y_coords': y_coords,
            'samples': segy.samples,
            'dt': segy.bin[segyio.BinField.Interval]
        }

def convert_to_xyz_format(sgy_file, output_dir='.'):
    """将SGY文件转换为X-Y-Time格式"""
    # 读取SGY文件
    segy_data = read_sgy_file(sgy_file)
    
    # 获取基本信息
    traces = segy_data['data']
    x_coords = segy_data['x_coords']
    y_coords = segy_data['y_coords']
    time = segy_data['samples']
    dt = segy_data['dt']
    
    n_traces, n_samples = traces.shape
    sample_interval = dt / 1000.0  # 转换为毫秒
    
    # 获取输出文件的基本名称
    base_name = os.path.splitext(os.path.basename(sgy_file))[0]
    
    print(f"开始转换数据...")
    print(f"数据维度: {traces.shape}")
    print(f"时间范围: {time[0]:.1f} - {time[-1]:.1f} ms")
    print(f"X坐标范围: {x_coords.min():,.0f} - {x_coords.max():,.0f}")
    print(f"Y坐标范围: {y_coords.min():,.0f} - {y_coords.max():,.0f}")

    # 保存基本数据 (使用调整后的X坐标)
    np.save(os.path.join(output_dir, f'{base_name}_x_coords.npy'), x_coords)
    np.save(os.path.join(output_dir, f'{base_name}_y_coords.npy'), y_coords)
    np.save(os.path.join(output_dir, f'{base_name}_time.npy'), time)
    np.save(os.path.join(output_dir, f'{base_name}_traces.npy'), traces)



    # 创建X-Y-Time-Amplitude格式数据
    print("创建X-Y-Time-Amplitude格式数据...")

    # 为每个时间样点创建X, Y, Time, Amplitude的记录 (使用调整后的X坐标)
    x_expanded = np.repeat(x_coords, n_samples)
    y_expanded = np.repeat(y_coords, n_samples)
    time_expanded = np.tile(time, n_traces)
    amplitude_expanded = traces.flatten()
    
    # 组合成4列数据: [X, Y, Time, Amplitude]
    trace_data = np.column_stack((x_expanded, y_expanded, time_expanded, amplitude_expanded))
    
    np.save(os.path.join(output_dir, f'{base_name}_xytz.npy'), trace_data)
    print(f"X-Y-Time-Amplitude数据保存完成: {trace_data.shape} (每行: X, Y, Time, Amplitude)")
    
    # 保存基本信息
    info = {
        'x_min': x_coords.min(),
        'x_max': x_coords.max(),
        'y_min': y_coords.min(),
        'y_max': y_coords.max(),
        'sample_interval': sample_interval,
        'n_samples': n_samples,
        'n_traces': n_traces,
        'time_start': time[0],
        'time_end': time[-1]
    }
    np.save(os.path.join(output_dir, f'{base_name}_info.npy'), info)
    
    # 如果有netCDF4库，创建NetCDF文件
    if HAS_NETCDF4:
        nc_file = os.path.join(output_dir, f'{base_name}_cgcs2000_fixed.nc')
        create_netcdf_xyz(nc_file, x_coords, y_coords, time, traces, trace_data, base_name)
    
    print(f"转换完成！生成的文件:")
    for ext in ['_x_coords.npy', '_y_coords.npy', '_time.npy', '_traces.npy', '_xytz.npy', '_info.npy']:
        file_path = os.path.join(output_dir, f"{base_name}{ext}")
        if os.path.exists(file_path):
            size = os.path.getsize(file_path) / (1024*1024)  # MB
            print(f"  - {os.path.basename(file_path)} ({size:.1f} MB)")
    
    if HAS_NETCDF4:
        nc_file = os.path.join(output_dir, f'{base_name}.nc')
        if os.path.exists(nc_file):
            size = os.path.getsize(nc_file) / (1024*1024)  # MB
            print(f"  - {os.path.basename(nc_file)} ({size:.1f} MB)")
    
    return base_name

def create_netcdf_xyz(nc_file, x_coords, y_coords, time, traces, trace_data, base_name):
    """创建ArcGIS Pro兼容的NetCDF文件"""
    print(f"正在创建ArcGIS Pro兼容的NetCDF文件: {nc_file}")

    # 如果文件存在，先删除
    if os.path.exists(nc_file):
        try:
            os.remove(nc_file)
            print(f"删除旧文件: {nc_file}")
        except Exception as e:
            print(f"无法删除旧文件: {e}")

    with netCDF4.Dataset(nc_file, 'w', format='NETCDF4') as ncfile:
        n_traces, n_samples = traces.shape

        # 创建规则网格用于ArcGIS Pro (X坐标已经在主函数中调整过)
        x_min, x_max = x_coords.min(), x_coords.max()
        y_min, y_max = y_coords.min(), y_coords.max()

        # 计算合适的网格分辨率
        unique_x = np.unique(x_coords)
        unique_y = np.unique(y_coords)

        if len(unique_x) > 1 and len(unique_y) > 1:
            x_res = np.median(np.diff(np.sort(unique_x)))
            y_res = np.median(np.diff(np.sort(unique_y)))
            grid_resolution = min(x_res, y_res)
        else:
            grid_resolution = min((x_max - x_min) / 100, (y_max - y_min) / 100)

        # 创建规则网格
        n_x = int((x_max - x_min) / grid_resolution) + 1
        n_y = int((y_max - y_min) / grid_resolution) + 1

        x_grid = np.linspace(x_min, x_max, n_x)
        y_grid = np.linspace(y_min, y_max, n_y)

        print(f"创建规则网格: {n_x} × {n_y} × {n_samples}")

        # 创建网格数据
        grid_data = np.full((n_y, n_x, n_samples), np.nan, dtype=np.float32)

        # 将原始数据插值到网格 (X坐标已经调整过)
        for i, (x, y) in enumerate(zip(x_coords, y_coords)):
            x_idx = np.argmin(np.abs(x_grid - x))
            y_idx = np.argmin(np.abs(y_grid - y))

            if (np.abs(x_grid[x_idx] - x) <= grid_resolution * 1.5 and
                np.abs(y_grid[y_idx] - y) <= grid_resolution * 1.5):
                grid_data[y_idx, x_idx, :] = traces[i, :]

        # 创建ArcGIS Pro认识的标准维度
        ncfile.createDimension('x', n_x)
        ncfile.createDimension('y', n_y)
        ncfile.createDimension('z', n_samples)  # 使用z而不是time

        # 创建坐标变量 - 必须与维度同名
        x_var = ncfile.createVariable('x', 'f8', ('x',))
        x_var[:] = x_grid
        x_var.long_name = 'x coordinate'
        x_var.standard_name = 'projection_x_coordinate'
        x_var.units = 'meters'
        x_var.axis = 'X'

        y_var = ncfile.createVariable('y', 'f8', ('y',))
        y_var[:] = y_grid
        y_var.long_name = 'y coordinate'
        y_var.standard_name = 'projection_y_coordinate'
        y_var.units = 'meters'
        y_var.axis = 'Y'

        z_var = ncfile.createVariable('z', 'f8', ('z',))
        z_var[:] = time
        z_var.long_name = 'time'
        z_var.standard_name = 'time'
        z_var.units = 'milliseconds since start of recording'
        z_var.axis = 'Z'
        z_var.positive = 'down'
        z_var._CoordinateAxisType = 'Time'

        # 创建边界变量 - ArcGIS Pro需要
        ncfile.createDimension('nv', 2)

        x_bnds = ncfile.createVariable('x_bnds', 'f8', ('x', 'nv'))
        y_bnds = ncfile.createVariable('y_bnds', 'f8', ('y', 'nv'))
        z_bnds = ncfile.createVariable('z_bnds', 'f8', ('z', 'nv'))

        # 创建nv坐标变量
        nv_var = ncfile.createVariable('nv', 'i4', ('nv',))
        nv_var[:] = [0, 1]
        nv_var.long_name = 'vertex number'

        # 计算边界值
        x_spacing = x_grid[1] - x_grid[0] if len(x_grid) > 1 else 1.0
        y_spacing = y_grid[1] - y_grid[0] if len(y_grid) > 1 else 1.0
        z_spacing = time[1] - time[0] if len(time) > 1 else 1.0

        for i in range(n_x):
            x_bnds[i, 0] = x_grid[i] - x_spacing/2
            x_bnds[i, 1] = x_grid[i] + x_spacing/2

        for i in range(n_y):
            y_bnds[i, 0] = y_grid[i] - y_spacing/2
            y_bnds[i, 1] = y_grid[i] + y_spacing/2

        for i in range(n_samples):
            z_bnds[i, 0] = time[i] - z_spacing/2
            z_bnds[i, 1] = time[i] + z_spacing/2

        # 设置边界属性
        x_var.bounds = 'x_bnds'
        x_var.actual_range = np.array([x_grid[0], x_grid[-1]])
        x_var._CoordinateAxisType = 'GeoX'

        y_var.bounds = 'y_bnds'
        y_var.actual_range = np.array([y_grid[0], y_grid[-1]])
        y_var._CoordinateAxisType = 'GeoY'

        z_var.bounds = 'z_bnds'
        z_var.actual_range = np.array([time[0], time[-1]])

        # 创建主数据变量 - 使用ArcGIS Pro偏好的维度顺序 (z, y, x)
        fill_value = -9999.0
        amplitude_var = ncfile.createVariable('amplitude', 'f4', ('z', 'y', 'x'),
                                            fill_value=fill_value, zlib=True, complevel=6)

        # 转置数据以匹配 (z, y, x) 维度顺序，并处理NaN值
        transposed_data = np.transpose(grid_data, (2, 0, 1))
        transposed_data = np.where(np.isnan(transposed_data), fill_value, transposed_data)
        amplitude_var[:] = transposed_data

        amplitude_var.long_name = 'Seismic amplitude'
        amplitude_var.standard_name = 'seismic_amplitude'
        amplitude_var.units = 'dimensionless'
        amplitude_var.coordinates = 'x y z'
        amplitude_var.grid_mapping = 'projection'  # 指向projection变量
        amplitude_var.valid_range = np.array([np.nanmin(traces), np.nanmax(traces)], dtype=np.float32)
        amplitude_var.scale_factor = 1.0
        amplitude_var.add_offset = 0.0
        amplitude_var.cell_methods = "area: mean"
        amplitude_var.display_type = "volume"
        amplitude_var.rendering_method = "continuous"

        # CGCS2000 3度带第37带的投影参数
        zone = 37
        central_meridian = 111.0  # 37 * 3 = 111度

        # CGCS2000 3度带第37带的ESRI投影字符串
        esri_pe_string = f'PROJCS["CGCS2000_3_Degree_GK_Zone_{zone}",GEOGCS["GCS_China_Geodetic_Coordinate_System_2000",DATUM["D_China_2000",SPHEROID["CGCS2000",6378137.0,298.257222101]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Gauss_Kruger"],PARAMETER["False_Easting",500000.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",{central_meridian}],PARAMETER["Scale_Factor",1.0],PARAMETER["Latitude_Of_Origin",0.0],UNIT["Meter",1.0]],VERTCS["Unknown VCS from ArcInfo Workstation",VDATUM["Unknown"],PARAMETER["Vertical_Shift",0.0],PARAMETER["Direction",1.0],UNIT["Meter",1.0]]'

        # 按照point_to_nc_ui.py的方式，将esri_pe_string设置在数据变量上
        amplitude_var.esri_pe_string = esri_pe_string

        # 创建投影变量（作为标量变量以避免警告）- 参考point_to_nc_ui.py
        projection = ncfile.createVariable('projection', 'i4', ())
        projection.grid_mapping_name = "transverse_mercator"

        # 设置投影参数 - 按照point_to_nc_ui.py的方式
        projection.longitude_of_central_meridian = central_meridian
        projection.latitude_of_projection_origin = 0.0
        projection.scale_factor_at_central_meridian = 1.0
        projection.false_easting = 500000.0
        projection.false_northing = 0.0
        projection.spatial_ref = esri_pe_string
        projection.GeoTransform = f"{x_grid[0]} {x_spacing} 0.0 {y_grid[-1]} 0.0 {-y_spacing}"

        # 添加全局属性 - CF约定和ArcGIS Pro优化
        ncfile.Conventions = 'CF-1.6'
        ncfile.title = f'Seismic data from {base_name}.sgy'
        ncfile.institution = 'SGY to NetCDF Converter'
        ncfile.source = f'{base_name}.sgy'
        ncfile.history = f'Created from SGY seismic data on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
        ncfile.references = f'CGCS2000_3_Degree_GK_Zone_{zone}'
        ncfile.comment = 'Seismic amplitude data interpolated to regular grid for ArcGIS Pro visualization'
        ncfile.Source_Software = 'Python NetCDF4'
        ncfile.spatial_ref = esri_pe_string
        ncfile.data_preservation = "interpolated"
        ncfile.interpolation_applied = "nearest_neighbor"
        ncfile.coordinate_system = f'CGCS2000 3 Degree GK Zone {zone}'
        ncfile.central_meridian = central_meridian
        ncfile.projection_zone = zone

        # 添加地理范围信息
        ncfile.geospatial_lat_min = float(y_grid.min())
        ncfile.geospatial_lat_max = float(y_grid.max())
        ncfile.geospatial_lon_min = float(x_grid.min())
        ncfile.geospatial_lon_max = float(x_grid.max())
        ncfile.geospatial_vertical_min = float(time.min())
        ncfile.geospatial_vertical_max = float(time.max())
        ncfile.GeoTransform = f"{x_grid[0]} {x_spacing} 0.0 {y_grid[-1]} 0.0 {-y_spacing}"

        # 数据信息
        ncfile.n_x = int(n_x)
        ncfile.n_y = int(n_y)
        ncfile.n_z = int(n_samples)
        ncfile.grid_resolution_x = float(x_spacing)
        ncfile.grid_resolution_y = float(y_spacing)
        ncfile.time_resolution = float(z_spacing)
        ncfile.original_traces = int(n_traces)

        # 统计有效数据点
        valid_count = np.sum(transposed_data != fill_value)
        total_count = n_x * n_y * n_samples
        ncfile.valid_data_points = int(valid_count)
        ncfile.total_grid_points = int(total_count)
        ncfile.data_coverage_percent = float(valid_count / total_count * 100)

    print(f"ArcGIS Pro兼容NetCDF文件创建完成: {nc_file}")
    print(f"网格大小: {n_x} × {n_y} × {n_samples}")
    print(f"网格分辨率: {grid_resolution:.1f} 米")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        sgy_file = sys.argv[1]
    else:
        sgy_file = "mig01.sgy"
    
    if not os.path.exists(sgy_file):
        print(f"错误: 文件不存在: {sgy_file}")
        return 1
    
    print("SGY到NetCDF转换工具 (X-Y-Time格式)")
    print("=" * 50)
    
    try:
        result = convert_to_xyz_format(sgy_file)
        print(f"\n转换成功完成!")
        return 0
    except Exception as e:
        print(f"转换失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
