#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试X-Y-Time格式的数据
验证转换后的数据是否正确
"""

import numpy as np
import os

def test_xyz_files(base_name="mig01"):
    """测试X-Y-Time格式的文件"""
    print(f"测试X-Y-Time格式文件 (基础名: {base_name})")
    print("=" * 50)
    
    # 检查文件是否存在
    files_to_check = [
        f"{base_name}_x_coords.npy",
        f"{base_name}_y_coords.npy", 
        f"{base_name}_time.npy",
        f"{base_name}_traces.npy",
        f"{base_name}_xytz.npy",
        f"{base_name}_info.npy"
    ]
    
    for filename in files_to_check:
        if os.path.exists(filename):
            try:
                data = np.load(filename, allow_pickle=True)
                size_mb = os.path.getsize(filename) / (1024*1024)
                
                if filename.endswith('_info.npy'):
                    print(f"✓ {filename}: {type(data)} ({size_mb:.1f} MB)")
                    if isinstance(data, dict):
                        for key, value in data.item().items():
                            print(f"    {key}: {value}")
                else:
                    print(f"✓ {filename}: {data.shape} {data.dtype} ({size_mb:.1f} MB)")
                    
                    if filename.endswith('_xytz.npy'):
                        print(f"    前5行数据:")
                        print(f"    X        Y        Time     Amplitude")
                        for i in range(min(5, len(data))):
                            print(f"    {data[i, 0]:8.1f} {data[i, 1]:8.1f} {data[i, 2]:8.1f} {data[i, 3]:8.3f}")
                        print(f"    数据范围:")
                        print(f"      X: {data[:, 0].min():.1f} - {data[:, 0].max():.1f}")
                        print(f"      Y: {data[:, 1].min():.1f} - {data[:, 1].max():.1f}")
                        print(f"      Time: {data[:, 2].min():.1f} - {data[:, 2].max():.1f}")
                        print(f"      Amplitude: {data[:, 3].min():.3f} - {data[:, 3].max():.3f}")
                    
                    elif filename.endswith('_traces.npy'):
                        print(f"    振幅范围: {data.min():.3f} - {data.max():.3f}")
                        print(f"    NaN数量: {np.sum(np.isnan(data))}")
                        
            except Exception as e:
                print(f"✗ {filename}: 读取失败 - {e}")
        else:
            print(f"✗ {filename}: 文件不存在")
    
    # 检查NetCDF文件
    nc_file = f"{base_name}.nc"
    if os.path.exists(nc_file):
        try:
            import netCDF4
            size_mb = os.path.getsize(nc_file) / (1024*1024)
            print(f"\n✓ {nc_file}: ({size_mb:.1f} MB)")
            
            with netCDF4.Dataset(nc_file, 'r') as ncfile:
                print(f"    格式: {ncfile.data_model}")
                print(f"    维度:")
                for dim_name, dim in ncfile.dimensions.items():
                    print(f"      {dim_name}: {len(dim)}")
                
                print(f"    变量:")
                for var_name, var in ncfile.variables.items():
                    print(f"      {var_name}: {var.shape}")
                
                print(f"    全局属性:")
                for attr in ['n_traces', 'n_samples', 'n_points', 'x_min', 'x_max', 'y_min', 'y_max']:
                    if hasattr(ncfile, attr):
                        print(f"      {attr}: {getattr(ncfile, attr)}")
                        
        except ImportError:
            print(f"✓ {nc_file}: 存在但无法读取 (需要netCDF4库)")
        except Exception as e:
            print(f"✗ {nc_file}: 读取失败 - {e}")
    else:
        print(f"\n✗ {nc_file}: 文件不存在")

def compare_with_original():
    """与原始数据比较"""
    print(f"\n与原始数据比较:")
    print("=" * 30)
    
    # 检查原始文件
    original_files = [
        "mig01_data.npy",
        "mig01_inlines.npy",
        "mig01_crosslines.npy"
    ]
    
    xyz_files = [
        "mig01_traces.npy",
        "mig01_x_coords.npy", 
        "mig01_y_coords.npy"
    ]
    
    print("原始格式文件:")
    for filename in original_files:
        if os.path.exists(filename):
            data = np.load(filename)
            size_mb = os.path.getsize(filename) / (1024*1024)
            print(f"  {filename}: {data.shape} ({size_mb:.1f} MB)")
        else:
            print(f"  {filename}: 不存在")
    
    print("\nX-Y-Time格式文件:")
    for filename in xyz_files:
        if os.path.exists(filename):
            data = np.load(filename)
            size_mb = os.path.getsize(filename) / (1024*1024)
            print(f"  {filename}: {data.shape} ({size_mb:.1f} MB)")
        else:
            print(f"  {filename}: 不存在")
    
    # 比较数据一致性
    if os.path.exists("mig01_traces.npy") and os.path.exists("mig01_data.npy"):
        traces_xyz = np.load("mig01_traces.npy")
        data_original = np.load("mig01_data.npy")
        
        print(f"\n数据一致性检查:")
        print(f"  X-Y-Time格式: {traces_xyz.shape}")
        print(f"  原始3D格式: {data_original.shape}")
        
        # 检查数据范围是否一致
        xyz_min, xyz_max = traces_xyz.min(), traces_xyz.max()
        orig_min, orig_max = np.nanmin(data_original), np.nanmax(data_original)
        
        print(f"  振幅范围比较:")
        print(f"    X-Y-Time: {xyz_min:.3f} - {xyz_max:.3f}")
        print(f"    原始3D:   {orig_min:.3f} - {orig_max:.3f}")
        print(f"    差异: {abs(xyz_min - orig_min):.6f}, {abs(xyz_max - orig_max):.6f}")

def main():
    """主函数"""
    print("X-Y-Time格式数据测试工具")
    print("=" * 50)
    
    # 测试X-Y-Time格式文件
    test_xyz_files("mig01")
    
    # 与原始数据比较
    compare_with_original()
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
