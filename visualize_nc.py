#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
NetCDF地震数据可视化工具
支持2D切片、3D体渲染、时间序列等多种可视化方式
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as colors
from matplotlib.widgets import Slider
import netCDF4
import os
import sys

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class NetCDFVisualizer:
    def __init__(self, nc_file):
        """初始化可视化器"""
        self.nc_file = nc_file
        self.load_data()
        
    def load_data(self):
        """加载NetCDF数据"""
        print(f"正在加载NetCDF文件: {self.nc_file}")
        
        with netCDF4.Dataset(self.nc_file, 'r') as ncfile:
            # 读取坐标
            self.x = ncfile.variables['x'][:]
            self.y = ncfile.variables['y'][:]
            self.z = ncfile.variables['z'][:]
            
            # 读取振幅数据
            self.amplitude = ncfile.variables['amplitude'][:]
            
            # 获取填充值
            self.fill_value = getattr(ncfile.variables['amplitude'], '_FillValue', -9999.0)
            
            # 处理填充值
            self.amplitude = np.ma.masked_equal(self.amplitude, self.fill_value)
            
            # 获取元数据
            self.title = getattr(ncfile, 'title', 'Seismic Data')
            self.coordinate_system = getattr(ncfile, 'coordinate_system', 'Unknown')
            
        print(f"数据形状: {self.amplitude.shape}")
        print(f"X范围: {self.x.min():.0f} - {self.x.max():.0f}")
        print(f"Y范围: {self.y.min():.0f} - {self.y.max():.0f}")
        print(f"Z范围: {self.z.min():.1f} - {self.z.max():.1f}")
        print(f"振幅范围: {self.amplitude.min():.3f} - {self.amplitude.max():.3f}")
        
    def plot_horizontal_slice(self, z_index=None, save_path=None):
        """绘制水平切片（平面图）"""
        if z_index is None:
            z_index = len(self.z) // 2  # 默认中间层
            
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # 获取切片数据
        slice_data = self.amplitude[z_index, :, :]
        
        # 创建网格
        X, Y = np.meshgrid(self.x, self.y)
        
        # 绘制等高线图
        im = ax.contourf(X, Y, slice_data, levels=50, cmap='seismic')
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('振幅', fontsize=12)
        
        # 设置标签和标题
        ax.set_xlabel('X坐标 (m)', fontsize=12)
        ax.set_ylabel('Y坐标 (m)', fontsize=12)
        ax.set_title(f'水平切片 - 深度: {self.z[z_index]:.1f}m\n{self.title}', fontsize=14)
        
        # 添加网格
        ax.grid(True, alpha=0.3)
        
        # 设置坐标轴格式
        ax.ticklabel_format(style='plain', axis='both')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"水平切片图已保存: {save_path}")
        
        plt.show()
        
    def plot_vertical_slice(self, direction='x', index=None, save_path=None):
        """绘制垂直切片"""
        if index is None:
            if direction == 'x':
                index = len(self.x) // 2
            else:
                index = len(self.y) // 2
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        if direction == 'x':
            # X方向切片 (固定X，显示Y-Z)
            slice_data = self.amplitude[:, :, index]
            Y, Z = np.meshgrid(self.y, self.z)
            im = ax.contourf(Y, Z, slice_data, levels=50, cmap='seismic')
            ax.set_xlabel('Y坐标 (m)', fontsize=12)
            ax.set_title(f'垂直切片 (X方向) - X: {self.x[index]:.0f}m', fontsize=14)
        else:
            # Y方向切片 (固定Y，显示X-Z)
            slice_data = self.amplitude[:, index, :]
            X, Z = np.meshgrid(self.x, self.z)
            im = ax.contourf(X, Z, slice_data, levels=50, cmap='seismic')
            ax.set_xlabel('X坐标 (m)', fontsize=12)
            ax.set_title(f'垂直切片 (Y方向) - Y: {self.y[index]:.0f}m', fontsize=14)
        
        ax.set_ylabel('深度 (m)', fontsize=12)
        ax.invert_yaxis()  # 深度向下
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('振幅', fontsize=12)
        
        # 添加网格
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"垂直切片图已保存: {save_path}")
        
        plt.show()
        
    def plot_interactive_slices(self):
        """交互式切片浏览器"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 初始索引
        z_idx = len(self.z) // 2
        x_idx = len(self.x) // 2
        y_idx = len(self.y) // 2
        
        # 创建滑块区域
        plt.subplots_adjust(bottom=0.25)
        ax_z = plt.axes([0.1, 0.1, 0.8, 0.03])
        ax_x = plt.axes([0.1, 0.05, 0.8, 0.03])
        ax_y = plt.axes([0.1, 0.15, 0.8, 0.03])
        
        # 创建滑块
        slider_z = Slider(ax_z, 'Z索引', 0, len(self.z)-1, valinit=z_idx, valfmt='%d')
        slider_x = Slider(ax_x, 'X索引', 0, len(self.x)-1, valinit=x_idx, valfmt='%d')
        slider_y = Slider(ax_y, 'Y索引', 0, len(self.y)-1, valinit=y_idx, valfmt='%d')
        
        def update_plots():
            """更新所有子图"""
            z_i = int(slider_z.val)
            x_i = int(slider_x.val)
            y_i = int(slider_y.val)
            
            # 清除所有子图
            for ax in [ax1, ax2, ax3, ax4]:
                ax.clear()
            
            # 水平切片 (Z固定)
            X, Y = np.meshgrid(self.x, self.y)
            slice_xy = self.amplitude[z_i, :, :]
            im1 = ax1.contourf(X, Y, slice_xy, levels=30, cmap='seismic')
            ax1.set_title(f'水平切片 Z={self.z[z_i]:.1f}m')
            ax1.set_xlabel('X (m)')
            ax1.set_ylabel('Y (m)')
            
            # 垂直切片 X-Z (Y固定)
            X_xz, Z_xz = np.meshgrid(self.x, self.z)
            slice_xz = self.amplitude[:, y_i, :]
            im2 = ax2.contourf(X_xz, Z_xz, slice_xz, levels=30, cmap='seismic')
            ax2.set_title(f'X-Z切片 Y={self.y[y_i]:.0f}m')
            ax2.set_xlabel('X (m)')
            ax2.set_ylabel('Z (m)')
            ax2.invert_yaxis()
            
            # 垂直切片 Y-Z (X固定)
            Y_yz, Z_yz = np.meshgrid(self.y, self.z)
            slice_yz = self.amplitude[:, :, x_i]
            im3 = ax3.contourf(Y_yz, Z_yz, slice_yz, levels=30, cmap='seismic')
            ax3.set_title(f'Y-Z切片 X={self.x[x_i]:.0f}m')
            ax3.set_xlabel('Y (m)')
            ax3.set_ylabel('Z (m)')
            ax3.invert_yaxis()
            
            # 时间序列 (单点)
            trace = self.amplitude[:, y_i, x_i]
            ax4.plot(self.z, trace, 'b-', linewidth=2)
            ax4.set_title(f'道记录 X={self.x[x_i]:.0f}m, Y={self.y[y_i]:.0f}m')
            ax4.set_xlabel('深度 (m)')
            ax4.set_ylabel('振幅')
            ax4.grid(True, alpha=0.3)
            ax4.invert_yaxis()
            
            plt.draw()
        
        # 绑定滑块事件
        slider_z.on_changed(lambda val: update_plots())
        slider_x.on_changed(lambda val: update_plots())
        slider_y.on_changed(lambda val: update_plots())
        
        # 初始绘制
        update_plots()
        
        plt.suptitle(f'交互式地震数据浏览器\n{self.title}', fontsize=16)
        plt.show()
        
    def plot_statistics(self, save_path=None):
        """绘制数据统计图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 振幅直方图
        valid_data = self.amplitude.compressed()  # 去除masked值
        ax1.hist(valid_data, bins=100, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.set_xlabel('振幅')
        ax1.set_ylabel('频次')
        ax1.set_title('振幅分布直方图')
        ax1.grid(True, alpha=0.3)
        
        # 数据覆盖率 (每层)
        coverage_by_layer = []
        for i in range(len(self.z)):
            layer = self.amplitude[i, :, :]
            valid_count = np.sum(~layer.mask) if hasattr(layer, 'mask') else layer.size
            total_count = layer.size
            coverage = valid_count / total_count * 100
            coverage_by_layer.append(coverage)
        
        ax2.plot(self.z, coverage_by_layer, 'r-', linewidth=2)
        ax2.set_xlabel('深度 (m)')
        ax2.set_ylabel('数据覆盖率 (%)')
        ax2.set_title('各深度层数据覆盖率')
        ax2.grid(True, alpha=0.3)
        ax2.invert_xaxis()
        
        # 平均振幅 (每层)
        mean_by_layer = []
        for i in range(len(self.z)):
            layer = self.amplitude[i, :, :]
            mean_val = np.ma.mean(layer)
            mean_by_layer.append(mean_val)
        
        ax3.plot(self.z, mean_by_layer, 'g-', linewidth=2)
        ax3.set_xlabel('深度 (m)')
        ax3.set_ylabel('平均振幅')
        ax3.set_title('各深度层平均振幅')
        ax3.grid(True, alpha=0.3)
        ax3.invert_xaxis()
        
        # 数据范围 (每层)
        min_by_layer = []
        max_by_layer = []
        for i in range(len(self.z)):
            layer = self.amplitude[i, :, :]
            min_val = np.ma.min(layer)
            max_val = np.ma.max(layer)
            min_by_layer.append(min_val)
            max_by_layer.append(max_val)
        
        ax4.fill_between(self.z, min_by_layer, max_by_layer, alpha=0.3, color='orange')
        ax4.plot(self.z, min_by_layer, 'b-', linewidth=1, label='最小值')
        ax4.plot(self.z, max_by_layer, 'r-', linewidth=1, label='最大值')
        ax4.set_xlabel('深度 (m)')
        ax4.set_ylabel('振幅范围')
        ax4.set_title('各深度层振幅范围')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.invert_xaxis()
        
        plt.suptitle(f'地震数据统计分析\n{self.title}', fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"统计图已保存: {save_path}")
        
        plt.show()
        
    def export_images(self, output_dir='./images'):
        """批量导出图像"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        print(f"正在导出图像到: {output_dir}")
        
        # 导出水平切片 (多个深度)
        z_indices = [0, len(self.z)//4, len(self.z)//2, 3*len(self.z)//4, len(self.z)-1]
        for i, z_idx in enumerate(z_indices):
            save_path = os.path.join(output_dir, f'horizontal_slice_{i+1}_z{self.z[z_idx]:.0f}m.png')
            self.plot_horizontal_slice(z_idx, save_path)
            plt.close()
        
        # 导出垂直切片
        x_idx = len(self.x) // 2
        y_idx = len(self.y) // 2
        
        save_path = os.path.join(output_dir, f'vertical_slice_x_direction.png')
        self.plot_vertical_slice('x', x_idx, save_path)
        plt.close()
        
        save_path = os.path.join(output_dir, f'vertical_slice_y_direction.png')
        self.plot_vertical_slice('y', y_idx, save_path)
        plt.close()
        
        # 导出统计图
        save_path = os.path.join(output_dir, f'statistics.png')
        self.plot_statistics(save_path)
        plt.close()
        
        print(f"所有图像已导出完成!")

def main():
    """主函数"""
    # 查找NetCDF文件
    nc_files = [f for f in os.listdir('.') if f.endswith('.nc')]
    
    if len(sys.argv) > 1:
        nc_file = sys.argv[1]
    elif nc_files:
        # 优先选择最新的文件
        nc_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        nc_file = nc_files[0]
        print(f"自动选择最新的NetCDF文件: {nc_file}")
    else:
        print("错误: 未找到NetCDF文件")
        print("用法: python visualize_nc.py [nc_file]")
        return 1
    
    if not os.path.exists(nc_file):
        print(f"错误: 文件不存在: {nc_file}")
        return 1
    
    print("NetCDF地震数据可视化工具")
    print("=" * 50)
    
    try:
        # 创建可视化器
        viz = NetCDFVisualizer(nc_file)
        
        # 显示菜单
        while True:
            print(f"\n可视化选项:")
            print(f"1. 水平切片 (平面图)")
            print(f"2. 垂直切片 (剖面图)")
            print(f"3. 交互式浏览器")
            print(f"4. 数据统计分析")
            print(f"5. 批量导出图像")
            print(f"6. 退出")
            
            choice = input(f"\n请选择 (1-6): ").strip()
            
            if choice == '1':
                viz.plot_horizontal_slice()
            elif choice == '2':
                direction = input("选择方向 (x/y): ").strip().lower()
                if direction in ['x', 'y']:
                    viz.plot_vertical_slice(direction)
                else:
                    print("无效方向，使用默认x方向")
                    viz.plot_vertical_slice('x')
            elif choice == '3':
                viz.plot_interactive_slices()
            elif choice == '4':
                viz.plot_statistics()
            elif choice == '5':
                viz.export_images()
            elif choice == '6':
                print("退出程序")
                break
            else:
                print("无效选择，请重新输入")
        
        return 0
        
    except Exception as e:
        print(f"可视化失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
