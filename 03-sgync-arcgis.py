#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SGY到NetCDF转换工具 - ArcGIS Pro兼容版本
将SEG-Y地震数据转换为ArcGIS Pro可识别的NetCDF格式
"""

import segyio
import numpy as np
import os
import sys
from datetime import datetime

# 尝试导入NetCDF4库
try:
    import netCDF4
    HAS_NETCDF4 = True
except ImportError:
    HAS_NETCDF4 = False
    print("警告: 未安装netCDF4库，将只保存numpy格式文件")

def read_sgy_file(filename):
    """读取SEG-Y文件并提取数据和道头信息"""
    print(f"正在读取SEG-Y文件: {filename}")
    
    with segyio.open(filename, strict=False) as segy:
        # 获取基本信息
        print(f"道数: {segy.tracecount}")
        print(f"采样点数: {len(segy.samples)}")
        print(f"采样率: {segy.bin[segyio.BinField.Interval]} 微秒")
        
        # 读取道头信息
        inlines = [segy.header[i][9] for i in range(segy.tracecount)]
        crosslines = [segy.header[i][21] for i in range(segy.tracecount)]
        x_coords = [segy.header[i][73] for i in range(segy.tracecount)]
        y_coords = [segy.header[i][77] for i in range(segy.tracecount)]
        
        # 转换为numpy数组
        inlines = np.array(inlines)
        crosslines = np.array(crosslines)
        x_coords = np.array(x_coords)
        y_coords = np.array(y_coords)
        
        # 读取地震数据
        seismic_data = np.stack([t.astype(float) for t in segy.trace])
        
        return {
            'data': seismic_data,
            'inlines': inlines,
            'crosslines': crosslines,
            'x_coords': x_coords,
            'y_coords': y_coords,
            'samples': segy.samples,
            'dt': segy.bin[segyio.BinField.Interval]
        }

def create_regular_grid(x_coords, y_coords, traces, grid_resolution=None):
    """创建规则网格用于ArcGIS Pro"""
    print("创建规则网格...")
    
    # 获取坐标范围
    x_min, x_max = x_coords.min(), x_coords.max()
    y_min, y_max = y_coords.min(), y_coords.max()
    
    print(f"X坐标范围: {x_min} - {x_max}")
    print(f"Y坐标范围: {y_min} - {y_max}")
    
    # 计算网格分辨率
    if grid_resolution is None:
        unique_x = np.unique(x_coords)
        unique_y = np.unique(y_coords)
        
        if len(unique_x) > 1 and len(unique_y) > 1:
            x_res = np.median(np.diff(np.sort(unique_x)))
            y_res = np.median(np.diff(np.sort(unique_y)))
            grid_resolution = min(x_res, y_res)
        else:
            # 如果坐标不规则，使用合理的分辨率
            grid_resolution = min((x_max - x_min) / 200, (y_max - y_min) / 200)
    
    print(f"网格分辨率: {grid_resolution}")
    
    # 创建规则网格
    n_x = int((x_max - x_min) / grid_resolution) + 1
    n_y = int((y_max - y_min) / grid_resolution) + 1
    
    x_grid = np.linspace(x_min, x_max, n_x)
    y_grid = np.linspace(y_min, y_max, n_y)
    
    print(f"网格大小: {n_x} × {n_y}")
    
    # 创建网格数据
    n_samples = traces.shape[1]
    grid_data = np.full((n_y, n_x, n_samples), np.nan, dtype=np.float32)
    
    # 将原始数据插值到网格
    print("插值数据到规则网格...")
    for i, (x, y) in enumerate(zip(x_coords, y_coords)):
        # 找到最近的网格点
        x_idx = np.argmin(np.abs(x_grid - x))
        y_idx = np.argmin(np.abs(y_grid - y))
        
        # 检查距离是否在合理范围内
        if (np.abs(x_grid[x_idx] - x) <= grid_resolution * 1.5 and 
            np.abs(y_grid[y_idx] - y) <= grid_resolution * 1.5):
            grid_data[y_idx, x_idx, :] = traces[i, :]
    
    # 统计有效数据点
    valid_points = np.sum(~np.isnan(grid_data[:, :, 0]))
    print(f"网格中有效数据点: {valid_points} / {n_x * n_y}")
    
    return x_grid, y_grid, grid_data

def create_arcgis_netcdf(nc_file, x_grid, y_grid, grid_data, time, base_name):
    """创建ArcGIS Pro兼容的NetCDF文件"""
    print(f"正在创建ArcGIS Pro兼容的NetCDF文件: {nc_file}")
    
    with netCDF4.Dataset(nc_file, 'w', format='NETCDF4') as ncfile:
        n_y, n_x, n_time = grid_data.shape
        
        # 创建维度 - 使用ArcGIS Pro认识的标准维度名
        ncfile.createDimension('x', n_x)
        ncfile.createDimension('y', n_y)
        ncfile.createDimension('time', n_time)
        
        # 创建坐标变量 - 必须与维度同名
        x_var = ncfile.createVariable('x', 'f8', ('x',))
        x_var[:] = x_grid
        x_var.long_name = 'x coordinate'
        x_var.standard_name = 'projection_x_coordinate'
        x_var.units = 'meters'
        x_var.axis = 'X'
        
        y_var = ncfile.createVariable('y', 'f8', ('y',))
        y_var[:] = y_grid
        y_var.long_name = 'y coordinate'
        y_var.standard_name = 'projection_y_coordinate'
        y_var.units = 'meters'
        y_var.axis = 'Y'
        
        time_var = ncfile.createVariable('time', 'f8', ('time',))
        time_var[:] = time
        time_var.long_name = 'time'
        time_var.standard_name = 'time'
        time_var.units = 'milliseconds since start of recording'
        time_var.axis = 'T'
        
        # 创建主数据变量 - 3D数据 (time, y, x) - ArcGIS Pro偏好的维度顺序
        amplitude_var = ncfile.createVariable('amplitude', 'f4', ('time', 'y', 'x'),
                                            fill_value=np.nan, zlib=True, complevel=6)
        
        # 转置数据以匹配 (time, y, x) 维度顺序
        amplitude_var[:] = np.transpose(grid_data, (2, 0, 1))
        
        amplitude_var.long_name = 'Seismic amplitude'
        amplitude_var.standard_name = 'seismic_amplitude'
        amplitude_var.units = 'dimensionless'
        amplitude_var.coordinates = 'x y time'
        amplitude_var.grid_mapping = 'crs'
        
        # 添加坐标参考系统信息
        crs_var = ncfile.createVariable('crs', 'i4')
        crs_var.grid_mapping_name = 'transverse_mercator'
        crs_var.long_name = 'Coordinate Reference System'
        crs_var.spatial_ref = 'PROJCS["Unknown",GEOGCS["GCS_Unknown",DATUM["D_Unknown",SPHEROID["Unknown",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Transverse_Mercator"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Scale_Factor",1.0],PARAMETER["Latitude_Of_Origin",0.0],UNIT["Meter",1.0]]'
        
        # 添加全局属性 - CF约定
        ncfile.Conventions = 'CF-1.6'
        ncfile.title = f'Seismic data from {base_name}.sgy'
        ncfile.institution = 'SGY to NetCDF Converter'
        ncfile.source = f'{base_name}.sgy'
        ncfile.history = f'Created on {datetime.now().isoformat()}'
        ncfile.references = 'CF-1.6 conventions'
        ncfile.comment = 'Seismic amplitude data in regular grid format for ArcGIS Pro'
        
        # 添加地理范围信息
        ncfile.geospatial_lat_min = float(y_grid.min())
        ncfile.geospatial_lat_max = float(y_grid.max())
        ncfile.geospatial_lon_min = float(x_grid.min())
        ncfile.geospatial_lon_max = float(x_grid.max())
        ncfile.geospatial_vertical_min = float(time.min())
        ncfile.geospatial_vertical_max = float(time.max())
        
        # 数据信息
        ncfile.n_x = int(n_x)
        ncfile.n_y = int(n_y)
        ncfile.n_time = int(n_time)
        ncfile.grid_resolution = float(x_grid[1] - x_grid[0])
        
    print(f"ArcGIS Pro兼容NetCDF文件创建完成: {nc_file}")

def convert_to_arcgis_format(sgy_file, output_dir='.', grid_resolution=None):
    """将SGY文件转换为ArcGIS Pro兼容格式"""
    # 读取SGY文件
    segy_data = read_sgy_file(sgy_file)
    
    # 获取基本信息
    traces = segy_data['data']
    x_coords = segy_data['x_coords']
    y_coords = segy_data['y_coords']
    time = segy_data['samples']
    
    # 获取输出文件的基本名称
    base_name = os.path.splitext(os.path.basename(sgy_file))[0]
    
    print(f"开始转换数据...")
    print(f"原始数据维度: {traces.shape}")
    print(f"时间范围: {time[0]:.1f} - {time[-1]:.1f} ms")
    
    # 创建规则网格
    x_grid, y_grid, grid_data = create_regular_grid(x_coords, y_coords, traces, grid_resolution)
    
    # 保存numpy格式数据
    np.save(os.path.join(output_dir, f'{base_name}_x_grid.npy'), x_grid)
    np.save(os.path.join(output_dir, f'{base_name}_y_grid.npy'), y_grid)
    np.save(os.path.join(output_dir, f'{base_name}_grid_data.npy'), grid_data)
    np.save(os.path.join(output_dir, f'{base_name}_time.npy'), time)
    
    # 保存原始数据（用于对比）
    np.save(os.path.join(output_dir, f'{base_name}_original_x.npy'), x_coords)
    np.save(os.path.join(output_dir, f'{base_name}_original_y.npy'), y_coords)
    np.save(os.path.join(output_dir, f'{base_name}_original_traces.npy'), traces)
    
    # 如果有netCDF4库，创建NetCDF文件
    if HAS_NETCDF4:
        nc_file = os.path.join(output_dir, f'{base_name}_arcgis.nc')
        create_arcgis_netcdf(nc_file, x_grid, y_grid, grid_data, time, base_name)
    
    print(f"\n转换完成！生成的文件:")
    files_to_check = [
        f'{base_name}_x_grid.npy',
        f'{base_name}_y_grid.npy', 
        f'{base_name}_grid_data.npy',
        f'{base_name}_time.npy',
        f'{base_name}_original_x.npy',
        f'{base_name}_original_y.npy',
        f'{base_name}_original_traces.npy'
    ]
    
    if HAS_NETCDF4:
        files_to_check.append(f'{base_name}_arcgis.nc')
    
    for filename in files_to_check:
        file_path = os.path.join(output_dir, filename)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path) / (1024*1024)  # MB
            print(f"  - {filename} ({size:.1f} MB)")
    
    return base_name

def main():
    """主函数"""
    if len(sys.argv) > 1:
        sgy_file = sys.argv[1]
        grid_res = float(sys.argv[2]) if len(sys.argv) > 2 else None
    else:
        sgy_file = "mig01.sgy"
        grid_res = None
    
    if not os.path.exists(sgy_file):
        print(f"错误: 文件不存在: {sgy_file}")
        return 1
    
    print("SGY到NetCDF转换工具 (ArcGIS Pro兼容版本)")
    print("=" * 60)
    
    try:
        result = convert_to_arcgis_format(sgy_file, grid_resolution=grid_res)
        print(f"\n转换成功完成!")
        print(f"\n使用说明:")
        print(f"1. 在ArcGIS Pro中添加NetCDF文件: {result}_arcgis.nc")
        print(f"2. 选择'amplitude'变量进行可视化")
        print(f"3. 可以按时间切片浏览3D数据")
        return 0
    except Exception as e:
        print(f"转换失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
