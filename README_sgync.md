# SGY到NetCDF转换工具 (03-sgync.py)

## 功能描述

`03-sgync.py` 是一个专门用于将SEG-Y地震数据转换为NetCDF格式的工具。它可以：

- 读取SEG-Y文件并提取地震数据和道头信息
- 将数据重组为3D立方体格式 (inline, crossline, time)
- 生成标准的NetCDF4文件，符合CF-1.6约定
- 同时保存numpy格式文件以确保兼容性
- 保留完整的坐标信息和道头数据

## 安装依赖

```bash
pip install segyio netcdf4 numpy
```

## 使用方法

### 1. 基本用法

```bash
# 转换单个文件（自动检测mig01.sgy）
python 03-sgync.py

# 指定输入文件
python 03-sgync.py input.sgy

# 指定输出文件
python 03-sgync.py input.sgy -o output.nc

# 不包含道头信息
python 03-sgync.py input.sgy --no-headers
```

### 2. 检查依赖库

```bash
python 03-sgync.py --check-deps
```

### 3. 在Python代码中使用

```python
from 03-sgync import convert_sgy_to_nc

# 转换文件
result = convert_sgy_to_nc('input.sgy', 'output.nc')
print(f"转换完成: {result}")
```

## 输出文件

转换完成后会生成以下文件：

### NetCDF文件 (.nc)
- **主文件**: 包含完整的3D地震数据立方体
- **维度**: inline, crossline, time
- **变量**: 
  - `amplitude`: 地震振幅数据 (inline, crossline, time)
  - `x_coordinate`, `y_coordinate`: 坐标网格
  - `inline`, `crossline`, `time`: 坐标轴
- **道头信息**: 存储在 `trace_headers` 组中

### Numpy文件 (.npy)
- `*_data.npy`: 主数据立方体 (143, 130, 1001)
- `*_inlines.npy`: Inline坐标数组
- `*_crosslines.npy`: Crossline坐标数组  
- `*_x_coords.npy`: X坐标网格
- `*_y_coords.npy`: Y坐标网格
- `*_grid.npy`: 网格元信息

## 数据格式说明

### 3D数据立方体
- **维度顺序**: (inline, crossline, time)
- **数据类型**: float32
- **缺失值**: 使用NaN填充不存在的道
- **坐标系**: 保持原始SGY文件的坐标系

### NetCDF变量属性
```
amplitude:
  - long_name: "Seismic amplitude"
  - units: "dimensionless"
  - coordinates: "x_coordinate y_coordinate"

time:
  - long_name: "Time"
  - units: "milliseconds"
  - standard_name: "time"

x_coordinate, y_coordinate:
  - long_name: "X/Y coordinate"
  - units: "meters"
```

### 全局属性
```
- source_file: 原始SGY文件名
- sample_interval_ms: 采样间隔(毫秒)
- n_samples: 时间样点数
- n_inlines: Inline数量
- n_crosslines: Crossline数量
- data_format: SGY数据格式
- created_by: "SGY to NetCDF Converter v1.0"
- creation_date: 创建时间
- conventions: "CF-1.6"
```

## 示例输出

```
SGY到NetCDF转换工具
==================================================
发现默认文件: mig01.sgy
开始转换...
正在读取SEG-Y文件: mig01.sgy
道数: 16972
采样点数: 1001
采样率: 1000 微秒
Inline范围: 1141 - 1283
Crossline范围: 1164 - 1293
Inline数量: 143
Crossline数量: 130
开始转换数据...
数据维度: (16972, 1001)
时间范围: 0.0 - 1000.0 ms
尝试创建3D立方体: (143, 130, 1001)
正在创建NetCDF文件: mig01.nc
NetCDF文件创建完成: mig01.nc
3D数据立方体保存完成: (143, 130, 1001)

转换完成!
输出文件: mig01.nc

生成的文件:
  - mig01.nc (45.9 MB)
  - mig01_data.npy (71.0 MB)
  - mig01_inlines.npy (0.0 MB)
  - mig01_crosslines.npy (0.0 MB)
  - mig01_x_coords.npy (0.1 MB)
  - mig01_y_coords.npy (0.1 MB)
  - mig01_grid.npy (0.0 MB)
```

## 读取转换后的数据

### 使用netCDF4
```python
import netCDF4 as nc
import numpy as np

with nc.Dataset('mig01.nc', 'r') as ncfile:
    # 读取振幅数据
    amplitude = ncfile.variables['amplitude'][:]
    
    # 读取坐标
    inlines = ncfile.variables['inline'][:]
    crosslines = ncfile.variables['crossline'][:]
    time = ncfile.variables['time'][:]
    
    # 读取坐标网格
    x_coords = ncfile.variables['x_coordinate'][:]
    y_coords = ncfile.variables['y_coordinate'][:]
```

### 使用numpy
```python
import numpy as np

# 读取主数据
data = np.load('mig01_data.npy')
inlines = np.load('mig01_inlines.npy')
crosslines = np.load('mig01_crosslines.npy')
x_coords = np.load('mig01_x_coords.npy')
y_coords = np.load('mig01_y_coords.npy')
grid_info = np.load('mig01_grid.npy', allow_pickle=True).item()
```

## 兼容性

- **Python**: 3.6+
- **依赖库**: segyio, netCDF4, numpy
- **输出格式**: NetCDF4, CF-1.6约定
- **数据兼容**: 与01-readsgy.py生成的格式完全兼容

## 注意事项

1. **内存使用**: 大文件转换时需要足够内存存储3D数据立方体
2. **缺失数据**: 不规则网格中的缺失道用NaN填充
3. **坐标系**: 保持原始SGY文件的坐标系，不进行坐标转换
4. **压缩**: NetCDF文件使用zlib压缩以减小文件大小

## 测试

使用 `test_nc.py` 验证转换结果：

```bash
python test_nc.py
```
