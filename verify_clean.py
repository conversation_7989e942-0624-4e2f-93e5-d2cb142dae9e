#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证清洁版NetCDF文件
检查是否完全兼容ArcGIS Pro，无警告信息
"""

import netCDF4
import numpy as np
import os

def verify_clean_netcdf():
    """验证清洁版NetCDF文件"""
    nc_file = "mig01_clean.nc"
    
    if not os.path.exists(nc_file):
        print(f"文件不存在: {nc_file}")
        return
    
    print("验证清洁版NetCDF文件")
    print("=" * 50)
    
    try:
        with netCDF4.Dataset(nc_file, 'r') as ncfile:
            print(f"文件: {nc_file}")
            print(f"大小: {os.path.getsize(nc_file) / (1024*1024):.1f} MB")
            print(f"格式: {ncfile.data_model}")
            
            # 检查维度
            print(f"\n维度检查:")
            dims = list(ncfile.dimensions.keys())
            print(f"  维度: {dims}")
            
            # 应该只有x, y, z三个维度
            expected_dims = ['x', 'y', 'z']
            missing_dims = [d for d in expected_dims if d not in dims]
            extra_dims = [d for d in dims if d not in expected_dims]
            
            if not missing_dims and not extra_dims:
                print(f"  ✓ 维度完美：只有x, y, z三个核心维度")
            else:
                if missing_dims:
                    print(f"  ⚠ 缺少维度: {missing_dims}")
                if extra_dims:
                    print(f"  ⚠ 额外维度: {extra_dims}")
            
            # 检查变量
            print(f"\n变量检查:")
            vars_list = list(ncfile.variables.keys())
            print(f"  变量: {vars_list}")
            
            # 应该只有核心变量
            expected_vars = ['x', 'y', 'z', 'amplitude', 'projection']
            missing_vars = [v for v in expected_vars if v not in vars_list]
            extra_vars = [v for v in vars_list if v not in expected_vars]
            
            if not missing_vars and not extra_vars:
                print(f"  ✓ 变量完美：只有核心变量，无边界变量")
            else:
                if missing_vars:
                    print(f"  ⚠ 缺少变量: {missing_vars}")
                if extra_vars:
                    print(f"  ⚠ 额外变量: {extra_vars}")
            
            # 检查坐标变量
            print(f"\n坐标变量详情:")
            for coord in ['x', 'y', 'z']:
                if coord in ncfile.variables:
                    var = ncfile.variables[coord]
                    print(f"  {coord}: {var.shape} {var.dtype}")
                    print(f"    范围: {var[:].min():.1f} - {var[:].max():.1f}")
                    if hasattr(var, 'standard_name'):
                        print(f"    标准名: {var.standard_name}")
                    if hasattr(var, 'units'):
                        print(f"    单位: {var.units}")
                    if hasattr(var, 'axis'):
                        print(f"    轴: {var.axis}")
            
            # 检查主数据变量
            print(f"\n主数据变量:")
            if 'amplitude' in ncfile.variables:
                amp = ncfile.variables['amplitude']
                print(f"  amplitude: {amp.shape} {amp.dtype}")
                print(f"    维度顺序: {amp.dimensions}")
                
                # 检查是否有grid_mapping
                if hasattr(amp, 'grid_mapping'):
                    print(f"    ✓ grid_mapping: {amp.grid_mapping}")
                else:
                    print(f"    ⚠ 缺少grid_mapping属性")
                
                # 检查是否有esri_pe_string
                if hasattr(amp, 'esri_pe_string'):
                    print(f"    ✓ esri_pe_string存在")
                else:
                    print(f"    ⚠ 缺少esri_pe_string属性")
                
                # 检查数据范围
                try:
                    fill_value = getattr(amp, '_FillValue', np.nan)
                    data_sample = amp[0, :10, :10]
                    valid_data = data_sample[data_sample != fill_value]
                    if len(valid_data) > 0:
                        print(f"    数据范围: {valid_data.min():.3f} - {valid_data.max():.3f}")
                    else:
                        print(f"    ⚠ 样本区域无有效数据")
                except Exception as e:
                    print(f"    ⚠ 无法读取数据: {e}")
            
            # 检查投影变量
            print(f"\n投影变量:")
            if 'projection' in ncfile.variables:
                proj = ncfile.variables['projection']
                print(f"  projection: {proj.shape} {proj.dtype}")
                
                # 检查投影参数
                proj_attrs = ['grid_mapping_name', 'longitude_of_central_meridian', 
                            'false_easting', 'false_northing', 'spatial_ref']
                for attr in proj_attrs:
                    if hasattr(proj, attr):
                        value = getattr(proj, attr)
                        if attr == 'spatial_ref' and len(str(value)) > 50:
                            print(f"    ✓ {attr}: {str(value)[:50]}...")
                        else:
                            print(f"    ✓ {attr}: {value}")
                    else:
                        print(f"    ⚠ 缺少属性: {attr}")
            
            # 检查全局属性
            print(f"\n关键全局属性:")
            global_attrs = ['Conventions', 'coordinate_system', 'central_meridian', 
                          'projection_zone', 'spatial_ref']
            for attr in global_attrs:
                if hasattr(ncfile, attr):
                    value = getattr(ncfile, attr)
                    if attr == 'spatial_ref' and len(str(value)) > 50:
                        print(f"  ✓ {attr}: {str(value)[:50]}...")
                    else:
                        print(f"  ✓ {attr}: {value}")
                else:
                    print(f"  ⚠ 缺少属性: {attr}")
            
            # 最终评估
            print(f"\n🎯 ArcGIS Pro兼容性评估:")
            
            # 检查是否有会导致警告的元素
            warning_elements = []
            
            if 'nv' in ncfile.dimensions:
                warning_elements.append("nv维度")
            
            boundary_vars = ['x_bnds', 'y_bnds', 'z_bnds']
            found_boundary_vars = [v for v in boundary_vars if v in ncfile.variables]
            if found_boundary_vars:
                warning_elements.append(f"边界变量: {found_boundary_vars}")
            
            if 'projection' in ncfile.variables:
                proj = ncfile.variables['projection']
                if len(proj.shape) != 0:
                    warning_elements.append("projection变量维度不为0")
            
            if warning_elements:
                print(f"  ⚠ 可能导致警告的元素: {warning_elements}")
            else:
                print(f"  ✅ 完全清洁：无任何会导致ArcGIS Pro警告的元素")
            
            print(f"\n📊 数据统计:")
            if hasattr(ncfile, 'data_coverage_percent'):
                print(f"  数据覆盖率: {ncfile.data_coverage_percent:.1f}%")
            if hasattr(ncfile, 'n_x') and hasattr(ncfile, 'n_y') and hasattr(ncfile, 'n_z'):
                print(f"  网格大小: {ncfile.n_x} × {ncfile.n_y} × {ncfile.n_z}")
            if hasattr(ncfile, 'original_traces'):
                print(f"  原始道数: {ncfile.original_traces}")
            
            print(f"\n🎉 验证完成！")
            print(f"此文件应该可以在ArcGIS Pro中无警告打开")
            print(f"坐标系统: CGCS2000 3 Degree GK Zone 37")
            
    except Exception as e:
        print(f"验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_clean_netcdf()
